#!/bin/bash
cd "$(dirname "$0")"

if [ -f app.pid ]; then
    PID=$(cat app.pid)
    if kill -0 $PID 2>/dev/null; then
        echo "停止應用程式 (PID: $PID)..."
        kill $PID
        sleep 2
        if kill -0 $PID 2>/dev/null; then
            echo "強制停止..."
            kill -9 $PID
        fi
        rm app.pid
        echo "✅ 應用程式已停止"
    else
        echo "應用程式未運行"
        rm app.pid
    fi
else
    echo "找不到 PID 文件，嘗試停止所有 node server.js 進程..."
    pkill -f "node server.js"
    echo "✅ 已嘗試停止所有相關進程"
fi
