#!/bin/bash

# 防詐騙教學平台 - 簡化 NAS 部署腳本
# 適用於沒有虛擬主機功能的 Web Station

echo "🚀 開始部署防詐騙教學平台 (簡化版)..."

# 檢查是否在正確的目錄
if [ ! -f "server.js" ]; then
    echo "❌ 請在專案根目錄執行此腳本"
    exit 1
fi

# 檢查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安裝，請先在套件中心安裝 Node.js"
    exit 1
fi

echo "📦 安裝依賴..."
npm install --production

if [ $? -ne 0 ]; then
    echo "❌ 依賴安裝失敗"
    exit 1
fi

# 創建必要目錄
echo "📁 創建目錄..."
mkdir -p data logs

# 創建環境變數文件
if [ ! -f ".env" ]; then
    echo "⚙️  創建環境變數..."
    JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || echo "change-this-secret-key-$(date +%s)")
    cat > .env << EOF
NODE_ENV=production
PORT=3001
JWT_SECRET=$JWT_SECRET
LOG_LEVEL=info
EOF
    echo "✅ 環境變數已創建"
fi

# 創建啟動腳本
echo "📝 創建管理腳本..."
cat > start-app.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"

# 載入環境變數
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 檢查是否已經在運行
if [ -f app.pid ] && kill -0 $(cat app.pid) 2>/dev/null; then
    echo "應用程式已在運行中 (PID: $(cat app.pid))"
    exit 1
fi

# 啟動應用程式
echo "啟動防詐騙教學平台..."
nohup node server.js > logs/app.log 2>&1 &
echo $! > app.pid

sleep 2
if kill -0 $(cat app.pid) 2>/dev/null; then
    echo "✅ 應用程式啟動成功 (PID: $(cat app.pid))"
    echo "🌐 訪問網址: http://$(hostname -I | awk '{print $1}'):${PORT:-3001}"
else
    echo "❌ 應用程式啟動失敗"
    rm -f app.pid
    exit 1
fi
EOF

# 創建停止腳本
cat > stop-app.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"

if [ -f app.pid ]; then
    PID=$(cat app.pid)
    if kill -0 $PID 2>/dev/null; then
        echo "停止應用程式 (PID: $PID)..."
        kill $PID
        sleep 2
        if kill -0 $PID 2>/dev/null; then
            echo "強制停止..."
            kill -9 $PID
        fi
        rm app.pid
        echo "✅ 應用程式已停止"
    else
        echo "應用程式未運行"
        rm app.pid
    fi
else
    echo "找不到 PID 文件"
fi
EOF

# 創建重啟腳本
cat > restart-app.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "重新啟動應用程式..."
./stop-app.sh
sleep 1
./start-app.sh
EOF

# 創建狀態檢查腳本
cat > status-app.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"

echo "=== 防詐騙教學平台狀態 ==="

# 檢查進程
if [ -f app.pid ] && kill -0 $(cat app.pid) 2>/dev/null; then
    PID=$(cat app.pid)
    echo "✅ 應用程式運行中 (PID: $PID)"
    
    # 檢查連接埠
    PORT=$(grep PORT .env | cut -d'=' -f2 || echo "3001")
    if netstat -tulpn 2>/dev/null | grep -q ":$PORT "; then
        echo "✅ 連接埠 $PORT 正在監聽"
        echo "🌐 訪問網址: http://$(hostname -I | awk '{print $1}'):$PORT"
    else
        echo "❌ 連接埠 $PORT 未監聽"
    fi
    
    # 記憶體使用
    MEM=$(ps -o pid,vsz,rss,comm -p $PID | tail -1)
    echo "📊 記憶體使用: $MEM"
else
    echo "❌ 應用程式未運行"
fi

# 磁碟空間
echo "💾 磁碟空間:"
df -h . | tail -1

# 最近日誌
if [ -f logs/app.log ]; then
    echo "📋 最近日誌 (最後5行):"
    tail -5 logs/app.log
fi
EOF

# 設定執行權限
chmod +x *.sh

echo ""
echo "🎉 部署準備完成！"
echo ""
echo "📋 接下來的步驟："
echo "1. 啟動應用程式："
echo "   ./start-app.sh"
echo ""
echo "2. 檢查狀態："
echo "   ./status-app.sh"
echo ""
echo "3. 停止應用程式："
echo "   ./stop-app.sh"
echo ""
echo "4. 重新啟動："
echo "   ./restart-app.sh"
echo ""
echo "5. 查看日誌："
echo "   tail -f logs/app.log"
echo ""
echo "🌐 預計訪問網址: http://$(hostname -I | awk '{print $1}'):3001"
echo ""
echo "🔧 如需開機自動啟動，請參考 simple-nas-deploy.md"
