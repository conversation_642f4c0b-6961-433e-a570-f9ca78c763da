<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 帳戶安全驗證 - 緊急通知 (詐騙模擬)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft JhengHei', sans-serif;
        }
        .support-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            border-top: 5px solid #dc3545;
        }
        .urgent-header {
            background: linear-gradient(45deg, #dc3545, #c0392b);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .security-alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
        }
        .verification-form {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 20px;
            border: 2px solid #dc3545;
        }
        .countdown-timer {
            background: #dc3545;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            animation: pulse 1s infinite;
        }
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(220, 53, 69, 0.95);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .warning-content {
            max-width: 700px;
            padding: 40px;
            background: white;
            color: #333;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-height: 90vh;
            overflow-y: auto;
        }
        .pulse-animation {
            animation: pulse 1s infinite;
        }
        .fake-logo {
            width: 120px;
            height: 40px;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 詐騙警告覆蓋層 -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-4x pulse-animation"></i>
            </div>
            <h2 class="text-danger mb-3">🚨 這是假客服詐騙！🚨</h2>
            <div class="alert alert-danger">
                <h5>您差點被騙了！</h5>
                <p class="mb-0">幸好這只是教育模擬，但在真實情況下，您的帳號密碼可能已經被竊取了！</p>
            </div>
            
            <div class="text-start mt-4">
                <h6 class="text-danger">這個假客服詐騙的特徵：</h6>
                <ul class="text-start">
                    <li>主動聯繫聲稱帳戶異常或被盜用</li>
                    <li>要求提供帳號密碼來「驗證身份」</li>
                    <li>催促立即處理，製造緊迫感</li>
                    <li>聯絡方式不是官方客服管道</li>
                    <li>可能冒充知名公司或銀行</li>
                    <li>要求透過網頁或電話提供敏感資訊</li>
                </ul>
                
                <h6 class="text-success">如何避免假客服詐騙：</h6>
                <ul class="text-start">
                    <li>✅ 真正的客服不會主動要求密碼</li>
                    <li>✅ 透過官方管道聯繫客服確認</li>
                    <li>✅ 不要透露個人敏感資訊</li>
                    <li>✅ 保持冷靜，不要被催促</li>
                    <li>✅ 有疑慮時掛斷電話重新撥打官方號碼</li>
                    <li>✅ 檢查官方網站的安全提醒</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="goBackToSimulation()">
                    <i class="fas fa-graduation-cap me-2"></i>我學會了
                </button>
                <button class="btn btn-primary btn-lg" onclick="goHome()">
                    <i class="fas fa-home me-2"></i>回到首頁
                </button>
            </div>
        </div>
    </div>

    <!-- 假客服詐騙內容 -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="support-container">
                    <div class="urgent-header">
                        <h1 class="mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            🔒 緊急安全通知
                        </h1>
                        <p class="mb-0">您的帳戶出現異常活動，需要立即驗證</p>
                    </div>
                    
                    <div class="countdown-timer">
                        <i class="fas fa-clock me-2"></i>
                        帳戶將在 <span id="countdown">15:00</span> 分鐘後被凍結
                    </div>
                    
                    <div class="p-4">
                        <div class="d-flex align-items-center mb-4">
                            <div class="fake-logo me-3">
                                <i class="fas fa-university me-2"></i>
                                台新銀行
                            </div>
                            <div>
                                <h5 class="mb-0">台新銀行客服中心</h5>
                                <small class="text-muted">官方安全驗證系統</small>
                            </div>
                        </div>
                        
                        <div class="security-alert">
                            <h5 class="text-warning">
                                <i class="fas fa-shield-alt me-2"></i>
                                安全警告
                            </h5>
                            <p class="mb-3">我們偵測到您的帳戶有以下異常活動：</p>
                            <ul>
                                <li>🚨 <strong>異地登入</strong>：來自中國北京的登入嘗試</li>
                                <li>🚨 <strong>密碼錯誤</strong>：連續5次密碼輸入錯誤</li>
                                <li>🚨 <strong>可疑交易</strong>：嘗試轉帳 NT$ 50,000</li>
                            </ul>
                            <div class="alert alert-danger mt-3">
                                <strong>立即行動：</strong>為保護您的資金安全，請立即完成身份驗證，否則帳戶將被暫時凍結。
                            </div>
                        </div>
                        
                        <div class="verification-form">
                            <h5 class="text-center mb-4 text-danger">
                                <i class="fas fa-user-shield me-2"></i>
                                身份驗證（必填）
                            </h5>
                            
                            <form id="fakeSupportForm">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-user me-2"></i>
                                        網路銀行帳號 *
                                    </label>
                                    <input type="text" class="form-control" placeholder="請輸入您的網路銀行帳號" required>
                                    <small class="text-muted">用於確認您的身份</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-lock me-2"></i>
                                        網路銀行密碼 *
                                    </label>
                                    <input type="password" class="form-control" placeholder="請輸入您的網路銀行密碼" required>
                                    <small class="text-muted">我們需要驗證您的身份以解除帳戶限制</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-mobile-alt me-2"></i>
                                        手機號碼 *
                                    </label>
                                    <input type="tel" class="form-control" placeholder="請輸入註冊的手機號碼" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-key me-2"></i>
                                        簡訊驗證碼 *
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="請輸入6位數驗證碼" required>
                                        <button class="btn btn-outline-secondary" type="button">
                                            發送驗證碼
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>重要提醒</h6>
                                    <p class="mb-0">完成驗證後，我們將立即解除您的帳戶限制並加強安全防護。此驗證過程完全免費且安全。</p>
                                </div>
                                
                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="urgent" required>
                                    <label class="form-check-label" for="urgent">
                                        我確認以上資訊正確，並同意進行緊急安全驗證
                                    </label>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger btn-lg">
                                        <i class="fas fa-shield-alt me-2"></i>
                                        立即驗證並解除限制
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <div class="text-center mt-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>
                                        客服專線：(02)2655-3355
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        24小時緊急服務
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-lock me-1"></i>
                        SSL 256位元加密保護 | 台新銀行官方驗證系統
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 倒數計時器
        let timeLeft = 15 * 60; // 15分鐘
        
        function updateCountdown() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('countdown').textContent = 
                String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
            
            if (timeLeft > 0) {
                timeLeft--;
            } else {
                timeLeft = 15 * 60; // 重置
            }
        }
        
        setInterval(updateCountdown, 1000);
        
        // 表單提交處理
        document.getElementById('fakeSupportForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>驗證中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                document.getElementById('warningOverlay').style.display = 'flex';
            }, 2000);
        });
        
        function goBackToSimulation() {
            window.location.href = '/scam-simulation';
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // 輸入提示
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (!this.hasAttribute('data-warned')) {
                    this.setAttribute('data-warned', 'true');
                    this.style.borderColor = '#dc3545';
                    
                    const tooltip = document.createElement('div');
                    tooltip.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    tooltip.style.top = '20px';
                    tooltip.style.right = '20px';
                    tooltip.style.zIndex = '1050';
                    tooltip.style.maxWidth = '300px';
                    tooltip.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>真正的客服絕不會要求您提供密碼！
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(tooltip);
                    
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.remove();
                        }
                    }, 5000);
                }
            });
        });
    </script>
</body>
</html>
