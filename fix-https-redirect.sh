#!/bin/bash

echo "🔧 修復所有 HTML 文件的 HTTPS 重定向問題..."

# 為所有 HTML 文件添加 force-http.js
for file in views/*.html; do
    if [ -f "$file" ]; then
        echo "處理文件: $file"
        
        # 檢查是否已經包含 force-http.js
        if ! grep -q "force-http.js" "$file"; then
            # 在 bootstrap.bundle.min.js 之後添加 force-http.js
            if grep -q "bootstrap.bundle.min.js" "$file"; then
                sed -i '/bootstrap.bundle.min.js/a\    <script src="/js/force-http.js"></script>' "$file"
                echo "  ✅ 已添加 force-http.js"
            else
                # 如果沒有 bootstrap，在 </body> 之前添加
                sed -i '/<\/body>/i\    <script src="/js/force-http.js"></script>' "$file"
                echo "  ✅ 已添加 force-http.js (在 body 結束前)"
            fi
        else
            echo "  ℹ️  已包含 force-http.js，跳過"
        fi
    fi
done

echo ""
echo "🎉 所有 HTML 文件已處理完成！"
echo ""
echo "📋 接下來的步驟："
echo "1. 重新啟動應用程式："
echo "   ./restart-app.sh"
echo ""
echo "2. 清除瀏覽器快取："
echo "   - 按 Ctrl+Shift+Delete"
echo "   - 選擇「所有時間」"
echo "   - 清除所有資料"
echo ""
echo "3. 使用無痕模式測試："
echo "   - 開啟無痕視窗"
echo "   - 訪問 http://************:8080"
echo ""
echo "4. 開啟開發者工具查看 Console 日誌"
echo "   - 按 F12"
echo "   - 切換到 Console 標籤"
echo "   - 查看是否有 'Force HTTP script loaded' 訊息"
