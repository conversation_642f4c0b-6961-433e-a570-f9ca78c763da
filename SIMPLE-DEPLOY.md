# 🚀 超簡單部署指南 (無需虛擬主機)

## 📋 準備工作 (2分鐘)

### 1. 安裝 Node.js
- 開啟 DSM 套件中心
- 搜尋並安裝 **Node.js v18** (或更新版本)

### 2. 啟用 SSH
- 控制台 → 終端機和 SNMP → 啟用 SSH 服務

## ⚡ 一鍵部署 (5分鐘)

### 步驟 1: 上傳文件
1. 開啟 **File Station**
2. 在任意位置創建資料夾 `fraud-prevention-platform`
3. 上傳所有專案文件到此資料夾

### 步驟 2: 執行部署
```bash
# SSH 連接到 NAS
ssh admin@你的NAS_IP

# 切換到專案目錄 (根據你的上傳位置調整)
cd /volume1/homes/admin/fraud-prevention-platform
# 或
cd /volume1/web/fraud-prevention-platform

# 執行一鍵部署
chmod +x simple-nas-deploy.sh
./simple-nas-deploy.sh
```

### 步驟 3: 啟動服務
```bash
# 啟動應用程式
./start-app.sh
```

### 步驟 4: 訪問網站
```
http://你的NAS_IP:3001
```

## 🎯 就這麼簡單！

現在您的防詐騙教學平台已經運行了！

## 🔧 常用命令

```bash
# 檢查狀態
./status-app.sh

# 停止服務
./stop-app.sh

# 重新啟動
./restart-app.sh

# 查看日誌
tail -f logs/app.log
```

## 🔄 開機自動啟動

### 使用任務排程器 (推薦)

1. **控制台 → 任務排程器**
2. **建立 → 觸發的任務 → 使用者定義的指令碼**
3. **設定**：
   ```
   任務名稱：防詐騙平台自動啟動
   使用者：admin
   事件：開機
   指令碼：/volume1/homes/admin/fraud-prevention-platform/start-app.sh
   ```

## 🛡️ 防火牆設定

1. **控制台 → 安全性 → 防火牆**
2. **新增規則**：
   - 連接埠：3001
   - 協定：TCP
   - 動作：允許

## 🌍 外網訪問 (可選)

### 路由器設定
1. 連接埠轉發：`8080` → `NAS_IP:3001`
2. 外網訪問：`http://你的公網IP:8080`

### DDNS 設定
1. 控制台 → 外部存取 → DDNS
2. 設定動態 DNS
3. 訪問：`http://你的域名:8080`

## 🆘 故障排除

### 無法啟動
```bash
# 檢查 Node.js
node --version

# 檢查依賴
npm list

# 檢查連接埠
netstat -tulpn | grep :3001
```

### 無法訪問
1. 檢查防火牆設定
2. 確認應用程式正在運行：`./status-app.sh`
3. 檢查日誌：`tail -f logs/app.log`

### 記憶體不足 (DS223j)
```bash
# 檢查記憶體使用
free -h

# 重新啟動 NAS (如果需要)
sudo reboot
```

## 📊 監控

### 系統資源監控
```bash
# CPU 使用率
top

# 記憶體使用
free -h

# 磁碟空間
df -h
```

### 應用程式監控
```bash
# 檢查應用程式狀態
./status-app.sh

# 即時日誌
tail -f logs/app.log
```

## 🔧 維護

### 定期維護 (建議每週)
```bash
# 重新啟動應用程式
./restart-app.sh

# 清理舊日誌
find logs/ -name "*.log" -mtime +7 -delete

# 檢查系統狀態
./status-app.sh
```

### 更新應用程式
1. 備份資料：`cp data/data.json backup/`
2. 上傳新文件
3. 重新啟動：`./restart-app.sh`

---

## 🎉 恭喜！

您的防詐騙教學平台現在已經成功運行在您的 Synology NAS 上了！

### 功能確認
- ✅ 防詐騙測驗
- ✅ 資安測驗  
- ✅ 金融常識測驗
- ✅ 詐騙模擬體驗
- ✅ 使用者註冊登入
- ✅ 學習進度追蹤

### 分享給其他人
現在其他人可以通過以下網址訪問：
- **內網**：`http://你的NAS_IP:3001`
- **外網**：`http://你的公網IP:8080` (需設定路由器)

讓更多人受益於防詐騙教育！ 🛡️
