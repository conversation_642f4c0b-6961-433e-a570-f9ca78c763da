# 防詐騙教學平台

一個全面的防詐騙、資安和金融常識教育平台，提供互動式測驗和詐騙模擬體驗。

## ✨ 功能特色

### 📚 教育測驗
- **防詐騙測驗** - 25 道精選題目，涵蓋常見詐騙手法
- **資安測驗** - 35 道題目，提升網路安全意識
- **金融常識測驗** - 34 道題目，建立正確理財觀念

### 🎭 詐騙模擬體驗
- **假銀行釣魚網站** - 體驗釣魚網站的危險
- **假購物網站** - 識別詐騙購物平台
- **假中獎通知** - 了解中獎詐騙手法
- **假投資平台** - 認識投資詐騙陷阱
- **假客服詐騙** - 防範假冒客服詐騙
- **感情詐騙** - 警惕網路感情詐騙

### 👤 個人化功能
- 使用者註冊和登入
- 學習進度追蹤
- 測驗成績記錄
- 個人資料管理

## 🌐 Web Station 部署 (推薦)

### 快速部署
```bash
# SSH 連接到 NAS
ssh admin@你的NAS_IP

# 切換到專案目錄
cd /volume1/web/fraud-prevention-platform

# 執行部署腳本
chmod +x webstation-setup.sh
./webstation-setup.sh

# 啟動應用程式
./start-app.sh
```

### 訪問應用程式
- **內網**：`http://NAS_IP:3001`
- **外網**：需要設定路由器連接埠轉發

### 管理命令
```bash
# 查看狀態
./status-app.sh

# 重新啟動
./restart-app.sh

# 停止服務
./stop-app.sh

# 查看日誌
tail -f logs/app.log
```

## 技術架構

### 前端技術
- HTML5, CSS3, JavaScript (ES6+)
- Bootstrap 5.3 (響應式框架)
- Font Awesome (圖示庫)

### 後端技術
- Node.js + Express.js
- MySQL 資料庫
- JWT 身份驗證
- bcryptjs 密碼加密

### 安全措施
- Helmet.js (HTTP 安全標頭)
- Express-rate-limit (速率限制)
- Express-validator (輸入驗證)
- CORS 跨域保護

## 安裝說明

### 系統需求
- Node.js 16.0 或以上版本
- MySQL 8.0 或以上版本
- npm 或 yarn 套件管理器

### 安裝步驟

1. **複製專案**
```bash
git clone [repository-url]
cd fraud-prevention-platform
```

2. **安裝相依套件**
```bash
npm install
```

3. **設定環境變數**
```bash
cp .env.example .env
```
編輯 `.env` 檔案，設定密鑰：
```
PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your-database-password
DB_NAME=fraud_prevention_db
```

4. **啟動服務**
```bash
# 開發模式
npm run dev

# 正式環境
npm start
```

5. **瀏覽網站**
開啟瀏覽器，前往 `http://localhost:3000`

### 注意事項

- 本專案目前使用記憶體資料庫模式，重啟伺服器後資料會重置
- 如需使用 MySQL 資料庫，請確保 MySQL 服務運行並建立對應資料庫
- 題庫包含 94 道題目，涵蓋防詐騙、資訊安全、金融常識三大領域

## 使用說明

### 使用者功能
1. **註冊帳號**：使用電子郵件註冊新帳號
2. **登入系統**：使用註冊的帳號登入
3. **選擇測驗**：從三大主題中選擇測驗類型
4. **開始答題**：系統隨機出10道題目
5. **查看結果**：測驗完成後查看分數和詳細解析
6. **追蹤進度**：查看個人學習記錄和最佳成績

### 管理功能
- 題庫管理：新增、編輯、刪除題目
- 使用者管理：查看使用者資料和活動記錄
- 成績統計：分析整體學習成效

## 題庫內容

### 防詐騙 (25題)
- 電話詐騙識別
- 網路詐騙防範
- 釣魚網站辨識
- 假投資詐騙
- 愛情詐騙防範

### 資訊安全 (25題)
- 密碼安全
- 社交工程攻擊
- 惡意軟體防範
- 網路安全基礎
- 個資保護

### 金融常識 (25題)
- 基本理財概念
- 投資風險管理
- 銀行金融服務
- 保險規劃
- 退休金準備

## 專案結構

```
fraud-prevention-platform/
├── public/                 # 靜態檔案
│   ├── css/               # 樣式檔案
│   ├── js/                # 前端JavaScript
│   └── images/            # 圖片資源
├── views/                 # HTML頁面
├── routes/                # API路由
├── models/                # 資料模型
├── middleware/            # 中介軟體
├── config/                # 設定檔
├── server.js              # 主程式
├── seed_questions.js      # 題庫初始化
├── package.json           # 套件設定
└── README.md             # 說明文件
```

## API 文件

### 認證相關
- `POST /api/auth/register` - 使用者註冊
- `POST /api/auth/login` - 使用者登入

### 測驗相關
- `GET /api/questions/:category` - 取得指定類別題目
- `POST /api/quiz/submit` - 提交測驗答案

### 進度相關
- `GET /api/progress` - 取得使用者進度

## 安全考量

### 已實作的安全措施
1. **密碼安全**：使用 bcryptjs 進行密碼雜湊
2. **身份驗證**：JWT Token 機制
3. **輸入驗證**：Express-validator 防止惡意輸入
4. **SQL 注入防護**：使用 Prepared Statements
5. **XSS 防護**：輸入輸出過濾
6. **CSRF 防護**：CORS 和 Helmet 設定
7. **速率限制**：防止暴力攻擊
8. **HTTPS 建議**：正式環境應使用 HTTPS

### 建議的額外安全措施
- 實作 CAPTCHA 驗證
- 加入登入嘗試鎖定機制
- 實作密碼複雜度檢查
- 定期安全性掃描

## 貢獻指南

歡迎貢獻到這個專案！請遵循以下步驟：

1. Fork 這個專案
2. 建立新的功能分支 (`git checkout -b feature/new-feature`)
3. 提交變更 (`git commit -am 'Add new feature'`)
4. 推送到分支 (`git push origin feature/new-feature`)
5. 建立 Pull Request

## 授權條款

本專案採用 MIT 授權條款。詳見 [LICENSE](LICENSE) 檔案。

## 聯絡資訊

如有任何問題或建議，請透過以下方式聯絡：

- Email: [<EMAIL>]
- GitHub Issues: [repository-url]/issues

## 更新日誌

### v1.0.0 (2024-01-01)
- 初始版本發布
- 實作基本的測驗功能
- 完成使用者認證系統
- 建立完整的題庫系統

---

感謝使用本平台，一起為網路安全和金融素養教育努力！