# 防詐騙教學平台 - Synology NAS 部署指南

## 📋 系統需求

- Synology DS223j (已確認)
- DSM 7.0 或更新版本
- Docker 套件
- 至少 1GB 可用空間
- 網路連接

## 🚀 部署步驟

### 步驟 1: 安裝 Docker 套件

1. 登入 Synology DSM
2. 開啟「套件中心」
3. 搜尋並安裝「Docker」套件
4. 等待安裝完成

### 步驟 2: 準備專案文件

1. 在 NAS 上創建資料夾：`/docker/fraud-prevention-platform`
2. 將以下文件上傳到該資料夾：
   - 所有專案文件（.js, .html, .css 等）
   - `package.json`
   - `Dockerfile`
   - `docker-compose.yml`
   - `healthcheck.js`

### 步驟 3: 使用 Docker Compose 部署

#### 方法 A: 透過 SSH（推薦）

1. 啟用 SSH 服務：
   - 控制台 → 終端機和 SNMP → 啟用 SSH 服務

2. 透過 SSH 連接到 NAS：
   ```bash
   ssh admin@你的NAS_IP地址
   ```

3. 切換到專案目錄：
   ```bash
   cd /volume1/docker/fraud-prevention-platform
   ```

4. 啟動服務：
   ```bash
   sudo docker-compose up -d
   ```

#### 方法 B: 透過 Docker GUI

1. 開啟 Docker 套件
2. 點選「映像」→「新增」→「從 Docker Hub 新增」
3. 搜尋 `node:18-alpine` 並下載
4. 點選「容器」→「建立」
5. 設定容器參數（參考下方設定）

### 步驟 4: 容器設定參數

如果使用 Docker GUI 建立容器，請使用以下設定：

**基本設定：**
- 容器名稱：`fraud-prevention-platform`
- 映像：`node:18-alpine`
- 重新啟動原則：`除非停止，否則一律重新啟動`

**連接埠設定：**
- 本機連接埠：`3000`
- 容器連接埠：`3000`

**磁碟區設定：**
- `/volume1/docker/fraud-prevention-platform` → `/app`
- `/volume1/docker/fraud-prevention-platform/data` → `/app/data`

**環境變數：**
- `NODE_ENV=production`
- `JWT_SECRET=your-super-secret-jwt-key-change-this`
- `PORT=3000`

### 步驟 5: 網路設定

#### 內網訪問
- 網址：`http://NAS_IP:3000`
- 例如：`http://*************:3000`

#### 外網訪問（可選）

1. **路由器設定：**
   - 設定連接埠轉發：外部連接埠 → NAS_IP:3000
   - 建議使用非標準連接埠（如 8080）提高安全性

2. **DDNS 設定：**
   - 控制台 → 外部存取 → DDNS
   - 設定動態 DNS 服務

3. **SSL 憑證（建議）：**
   - 控制台 → 安全性 → 憑證
   - 申請 Let's Encrypt 憑證

### 步驟 6: 防火牆設定

1. 控制台 → 安全性 → 防火牆
2. 新增規則允許連接埠 3000
3. 設定來源 IP 限制（可選）

## 🔧 維護和監控

### 檢查容器狀態
```bash
sudo docker ps
sudo docker logs fraud-prevention-platform
```

### 更新應用程式
```bash
sudo docker-compose down
sudo docker-compose pull
sudo docker-compose up -d
```

### 備份資料
定期備份 `/volume1/docker/fraud-prevention-platform/data` 資料夾

## 🛡️ 安全建議

1. **更改預設密碼：**
   - 修改 `JWT_SECRET` 環境變數

2. **限制訪問：**
   - 設定防火牆規則
   - 使用 VPN 連接

3. **定期更新：**
   - 更新 DSM 系統
   - 更新 Docker 映像

4. **監控日誌：**
   - 定期檢查應用程式日誌
   - 設定異常警報

## 📊 效能優化

### DS223j 特定優化

1. **記憶體限制：**
   ```yaml
   deploy:
     resources:
       limits:
         memory: 512M
   ```

2. **CPU 限制：**
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '0.5'
   ```

## 🆘 故障排除

### 常見問題

1. **容器無法啟動：**
   - 檢查連接埠是否被占用
   - 確認檔案權限正確

2. **無法訪問網站：**
   - 檢查防火牆設定
   - 確認連接埠轉發正確

3. **效能問題：**
   - 監控 CPU 和記憶體使用量
   - 考慮調整資源限制

### 日誌位置
- 應用程式日誌：`/volume1/docker/fraud-prevention-platform/logs`
- Docker 日誌：`sudo docker logs fraud-prevention-platform`

## 📞 支援

如遇到問題，請檢查：
1. DSM 版本相容性
2. Docker 套件版本
3. 網路連接狀態
4. 系統資源使用情況

---

**注意：** DS223j 是入門級 NAS，建議監控系統資源使用情況，必要時調整容器資源限制。
