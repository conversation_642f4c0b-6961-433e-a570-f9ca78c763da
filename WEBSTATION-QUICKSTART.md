# 🚀 Web Station 快速部署指南

## 📋 準備工作 (5分鐘)

### 1. 安裝必要套件
在 Synology DSM 套件中心安裝：
- ✅ **Web Station** 
- ✅ **Node.js v18** (或更新版本)

### 2. 上傳專案文件
1. 開啟 **File Station**
2. 導航到 `/web` 資料夾
3. 創建資料夾：`fraud-prevention-platform`
4. 上傳所有專案文件到此資料夾

## ⚡ 快速部署 (3分鐘)

### 步驟 1: 安裝依賴
```bash
# SSH 連接到 NAS
ssh admin@你的NAS_IP

# 切換到專案目錄
cd /volume1/web/fraud-prevention-platform

# 執行設定腳本
chmod +x webstation-setup.sh
./webstation-setup.sh
```

### 步驟 2: 設定 Web Station
1. 開啟 **Web Station** 套件
2. 點選「**虛擬主機**」→「**建立**」
3. 填入設定：

```
📝 基本設定：
主機名稱：fraud-prevention
連接埠：3001
文件根目錄：/web/fraud-prevention-platform

🔧 後端伺服器：
✅ 啟用後端伺服器
類型：Node.js
啟動檔案：server.js
環境：production

🌍 環境變數：
NODE_ENV=production
JWT_SECRET=your-generated-secret-key
PORT=3001
```

### 步驟 3: 啟動服務
1. 點選「**啟用**」虛擬主機
2. 等待狀態變為「**正常**」

## 🌐 訪問網站

### 內網訪問
```
http://你的NAS_IP:3001
例如：http://*************:3001
```

### 外網訪問（可選）
1. **路由器設定**：連接埠轉發 8080 → NAS_IP:3001
2. **DDNS 設定**：控制台 → 外部存取 → DDNS
3. **訪問**：`http://你的DDNS域名:8080`

## 🛡️ 安全設定

### 防火牆
1. 控制台 → 安全性 → 防火牆
2. 新增規則允許連接埠 3001

### SSL 憑證（建議）
1. 控制台 → 安全性 → 憑證
2. 新增 → Let's Encrypt
3. Web Station → 虛擬主機 → 編輯 → 套用憑證

## 🔧 常用操作

### 重新啟動服務
```bash
# 方法 1: Web Station GUI
Web Station → 虛擬主機 → 停用 → 啟用

# 方法 2: SSH 命令
cd /volume1/web/fraud-prevention-platform
npm run restart
```

### 查看日誌
```bash
# 應用程式日誌
npm run logs

# 或直接查看
tail -f logs/app.log
```

### 檢查狀態
```bash
# 檢查 Node.js 進程
ps aux | grep node

# 檢查連接埠
netstat -tulpn | grep :3001
```

## 🆘 故障排除

### 虛擬主機無法啟動
1. 檢查 Node.js 套件是否安裝
2. 確認專案目錄路徑正確
3. 檢查 package.json 是否存在

### 無法訪問網站
1. 確認虛擬主機狀態為「正常」
2. 檢查防火牆設定
3. 驗證連接埠 3001 是否開放

### 效能問題
```bash
# 監控系統資源
top
free -h
df -h
```

## 📊 DS223j 優化建議

由於 DS223j 是入門級 NAS：

1. **記憶體管理**
   - 關閉不必要的套件
   - 定期重新啟動服務

2. **效能監控**
   - 定期檢查 CPU 使用率
   - 監控記憶體使用情況

3. **維護計劃**
   - 每週重新啟動一次
   - 定期清理日誌文件

## 📞 需要幫助？

如果遇到問題：
1. 檢查 Web Station 虛擬主機狀態
2. 查看應用程式日誌：`npm run logs`
3. 檢查系統資源使用情況
4. 參考完整部署指南：`webstation-deploy-guide.md`

---

**🎉 恭喜！您的防詐騙教學平台已成功部署！**

現在您可以：
- ✅ 進行防詐騙測驗
- ✅ 體驗詐騙模擬
- ✅ 追蹤學習進度
- ✅ 讓其他人也能訪問學習
