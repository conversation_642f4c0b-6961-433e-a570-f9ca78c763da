#!/bin/bash
cd "$(dirname "$0")"

echo "=== 防詐騙教學平台狀態 ==="

# 檢查進程
if [ -f app.pid ] && kill -0 $(cat app.pid) 2>/dev/null; then
    PID=$(cat app.pid)
    echo "✅ 應用程式運行中 (PID: $PID)"
    
    # 檢查連接埠
    PORT=$(grep PORT .env | cut -d'=' -f2 || echo "9999")
    if netstat -tulpn 2>/dev/null | grep -q ":$PORT "; then
        echo "✅ 連接埠 $PORT 正在監聽"
        echo "🌐 訪問網址: http://$(ip route get 1 | awk '{print $7}' | head -1):$PORT"
    else
        echo "❌ 連接埠 $PORT 未監聽"
    fi
    
    # 記憶體使用
    MEM=$(ps -o pid,vsz,rss,comm -p $PID | tail -1)
    echo "📊 記憶體使用: $MEM"
else
    echo "❌ 應用程式未運行"
fi

# 磁碟空間
echo "💾 磁碟空間:"
df -h . | tail -1

# 最近日誌
if [ -f logs/app.log ]; then
    echo "📋 最近日誌 (最後5行):"
    tail -5 logs/app.log
fi
