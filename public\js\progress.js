document.addEventListener('DOMContentLoaded', function() {
    checkAuthAndLoadProgress();
});

function checkAuthAndLoadProgress() {
    const token = localStorage.getItem('token');
    
    if (!token) {
        showLoginRequired();
        return;
    }
    
    loadProgressData();
}

function showLoginRequired() {
    document.getElementById('loading').classList.add('d-none');
    document.getElementById('login-required').classList.remove('d-none');
}

function showProgressContent() {
    document.getElementById('loading').classList.add('d-none');
    document.getElementById('login-required').classList.add('d-none');
    document.getElementById('progress-content').classList.remove('d-none');
}

async function loadProgressData() {
    try {
        const response = await apiRequest('/api/progress');
        
        if (response.error) {
            showAlert(response.error, 'danger');
            return;
        }
        
        displayProgressData(response);
        showProgressContent();
        
    } catch (error) {
        console.error('載入進度資料錯誤:', error);
        showAlert('載入進度資料失敗，請稍後再試', 'danger');
        showLoginRequired();
    }
}

function displayProgressData(data) {
    const { progress, recentSessions } = data;
    
    // 計算總體統計
    let totalAttempts = 0;
    let totalScore = 0;
    let lastActivity = null;
    
    progress.forEach(item => {
        totalAttempts += item.total_attempts;
        totalScore += item.best_score * item.total_attempts;
        
        const activityDate = new Date(item.last_attempt_at);
        if (!lastActivity || activityDate > lastActivity) {
            lastActivity = activityDate;
        }
    });
    
    const averageScore = totalAttempts > 0 ? Math.round(totalScore / totalAttempts) : 0;
    
    // 更新總體統計
    document.getElementById('total-attempts').textContent = totalAttempts;
    document.getElementById('average-score').textContent = averageScore + '%';
    document.getElementById('last-activity').textContent = lastActivity ? 
        formatDate(lastActivity) : '尚未開始';
    
    // 顯示各類別進度
    displayCategoryProgress(progress);
    
    // 顯示最近測驗記錄
    displayRecentSessions(recentSessions);
}

function displayCategoryProgress(progress) {
    const container = document.getElementById('category-progress');
    const categories = {
        'fraud': { name: '防詐騙', icon: 'fas fa-exclamation-triangle', color: 'warning' },
        'cybersecurity': { name: '資訊安全', icon: 'fas fa-shield-alt', color: 'success' },
        'finance': { name: '金融常識', icon: 'fas fa-coins', color: 'info' }
    };
    
    let html = '';
    
    Object.keys(categories).forEach(categoryKey => {
        const category = categories[categoryKey];
        const progressData = progress.find(p => p.category === categoryKey);
        
        const bestScore = progressData ? progressData.best_score : 0;
        const attempts = progressData ? progressData.total_attempts : 0;
        const lastAttempt = progressData ? formatDate(new Date(progressData.last_attempt_at)) : '尚未開始';
        
        html += `
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="${category.icon} fa-2x text-${category.color} me-3"></i>
                        <div>
                            <h6 class="mb-0">${category.name}</h6>
                            <small class="text-muted">測驗次數: ${attempts}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="progress mb-2" style="height: 25px;">
                        <div class="progress-bar bg-${category.color}" role="progressbar" 
                             style="width: ${bestScore}%" aria-valuenow="${bestScore}" 
                             aria-valuemin="0" aria-valuemax="100">
                            ${bestScore}%
                        </div>
                    </div>
                    <small class="text-muted">最後測驗: ${lastAttempt}</small>
                </div>
            </div>
        `;
    });
    
    if (html === '') {
        html = '<p class="text-muted text-center">尚無測驗記錄，<a href="/quiz">立即開始測驗</a></p>';
    }
    
    container.innerHTML = html;
}

function displayRecentSessions(sessions) {
    const container = document.getElementById('recent-sessions');
    const categories = {
        'fraud': { name: '防詐騙', color: 'warning' },
        'cybersecurity': { name: '資訊安全', color: 'success' },
        'finance': { name: '金融常識', color: 'info' }
    };
    
    if (sessions.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">尚無測驗記錄</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead>
            <tr>
                <th>測驗類型</th>
                <th>分數</th>
                <th>完成時間</th>
                <th>評價</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    sessions.forEach(session => {
        const category = categories[session.category];
        const score = session.score;
        const completedAt = formatDateTime(new Date(session.completed_at));
        
        let scoreClass = 'text-danger';
        let evaluation = '需要加強';
        
        if (score >= 90) {
            scoreClass = 'text-success';
            evaluation = '優秀';
        } else if (score >= 70) {
            scoreClass = 'text-info';
            evaluation = '良好';
        } else if (score >= 50) {
            scoreClass = 'text-warning';
            evaluation = '及格';
        }
        
        html += `
            <tr>
                <td>
                    <span class="badge bg-${category.color}">${category.name}</span>
                </td>
                <td class="${scoreClass} fw-bold">${score}%</td>
                <td>${completedAt}</td>
                <td>${evaluation}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

function formatDate(date) {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
        return '昨天';
    } else if (diffDays <= 7) {
        return `${diffDays} 天前`;
    } else {
        return date.toLocaleDateString('zh-TW');
    }
}

function formatDateTime(date) {
    return date.toLocaleString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}
