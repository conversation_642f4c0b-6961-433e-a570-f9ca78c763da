<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 金鑽投資 - 穩賺不賠的投資機會 (詐騙模擬)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            min-height: 100vh;
            font-family: 'Microsoft JhengHei', sans-serif;
        }
        .investment-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header-banner {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .profit-display {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            text-align: center;
            margin: 20px;
            border-radius: 15px;
        }
        .profit-number {
            font-size: 48px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        .testimonial-card {
            background: #f8f9fa;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .urgent-offer {
            background: #e74c3c;
            color: white;
            padding: 15px;
            text-align: center;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
        .fake-chart {
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMjdBRTYwO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMyRUNDNzE7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y4ZjlmYSIvPgogIDxwb2x5bGluZSBmaWxsPSJub25lIiBzdHJva2U9InVybCgjZ3JhZGllbnQpIiBzdHJva2Utd2lkdGg9IjMiIHBvaW50cz0iMjAsMTgwIDgwLDE2MCA5MSwxNDAgMTIwLDEyMCAxNTAsMTAwIDE4MCw4MCAyMTAsNjAgMjQwLDQwIDI3MCwyMCAzMDAsMTAgMzMwLDUgMzgwLDIiLz4KICA8dGV4dCB4PSIyMDAiIHk9IjEwMCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzJFQ0M3MSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+📈 持續上漲中</dGV4dD4KPC9zdmc+') no-repeat center;
            background-size: contain;
            height: 200px;
            border: 2px solid #27ae60;
            border-radius: 10px;
            margin: 20px 0;
        }
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(220, 53, 69, 0.95);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .warning-content {
            max-width: 700px;
            padding: 40px;
            background: white;
            color: #333;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-height: 90vh;
            overflow-y: auto;
        }
        .pulse-animation {
            animation: pulse 1s infinite;
        }
        .success-badge {
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 詐騙警告覆蓋層 -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-4x pulse-animation"></i>
            </div>
            <h2 class="text-danger mb-3">🚨 這是投資詐騙！🚨</h2>
            <div class="alert alert-danger">
                <h5>您差點被騙了！</h5>
                <p class="mb-0">幸好這只是教育模擬，但在真實情況下，您可能已經損失大筆金錢了！</p>
            </div>
            
            <div class="text-start mt-4">
                <h6 class="text-danger">這個假投資平台的詐騙特徵：</h6>
                <ul class="text-start">
                    <li>承諾不合理的高報酬（月獲利30%）</li>
                    <li>聲稱零風險或保證獲利</li>
                    <li>要求快速決定，製造緊迫感</li>
                    <li>缺乏合法的金融牌照和監管資訊</li>
                    <li>使用假的成功案例和推薦</li>
                    <li>要求先投入資金才能看到更多資訊</li>
                    <li>聯絡方式不透明或只有網路管道</li>
                </ul>
                
                <h6 class="text-success">如何避免投資詐騙：</h6>
                <ul class="text-start">
                    <li>✅ 記住：高報酬必然伴隨高風險</li>
                    <li>✅ 檢查是否有合法的金融牌照</li>
                    <li>✅ 不要相信網路陌生人的投資建議</li>
                    <li>✅ 諮詢專業的理財顧問</li>
                    <li>✅ 分散投資，不要孤注一擲</li>
                    <li>✅ 投資前充分了解產品和風險</li>
                    <li>✅ 選擇有信譽的金融機構</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="goBackToSimulation()">
                    <i class="fas fa-graduation-cap me-2"></i>我學會了
                </button>
                <button class="btn btn-primary btn-lg" onclick="goHome()">
                    <i class="fas fa-home me-2"></i>回到首頁
                </button>
            </div>
        </div>
    </div>

    <!-- 假投資平台內容 -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="investment-container">
                    <div class="header-banner">
                        <h1 class="mb-2">
                            <i class="fas fa-gem me-2"></i>
                            💰 金鑽投資平台 💰
                        </h1>
                        <p class="mb-0">全球頂尖AI智能投資系統 | 穩賺不賠 | 月獲利保證30%</p>
                    </div>
                    
                    <div class="urgent-offer">
                        <h5><i class="fas fa-fire me-2"></i>限時優惠！今日加入享首月免手續費！</h5>
                    </div>
                    
                    <div class="row p-4">
                        <div class="col-md-8">
                            <div class="profit-display">
                                <h3>本月平台總獲利</h3>
                                <div class="profit-number">+847%</div>
                                <p class="mb-0">已幫助10,000+投資者實現財富自由</p>
                            </div>
                            
                            <div class="fake-chart"></div>
                            
                            <h5 class="mb-3">
                                <i class="fas fa-trophy me-2"></i>
                                成功案例分享
                            </h5>
                            
                            <div class="testimonial-card">
                                <div class="d-flex align-items-center mb-2">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2Yzc1N2QiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5王</dGV4dD4KPC9zdmc+" 
                                         class="me-3" alt="用戶頭像">
                                    <div>
                                        <strong>王先生 (台北)</strong>
                                        <span class="success-badge ms-2">已獲利 NT$ 2,500,000</span>
                                    </div>
                                </div>
                                <p class="mb-0">"我只投入了10萬，3個月就賺到250萬！這個AI系統真的太神奇了，現在我已經辭職專心投資了！"</p>
                            </div>
                            
                            <div class="testimonial-card">
                                <div class="d-flex align-items-center mb-2">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNlNzRjM2MiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxMiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5李</dGV4dD4KPC9zdmc+" 
                                         class="me-3" alt="用戶頭像">
                                    <div>
                                        <strong>李小姐 (高雄)</strong>
                                        <span class="success-badge ms-2">已獲利 NT$ 1,800,000</span>
                                    </div>
                                </div>
                                <p class="mb-0">"原本只是想試試看，沒想到真的每天都在賺錢！現在我把所有積蓄都投入了，準備買房子！"</p>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-rocket me-2"></i>
                                        立即開始投資
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-gift me-2"></i>新手專享</h6>
                                        <ul class="mb-0">
                                            <li>首次投資免手續費</li>
                                            <li>專屬投資顧問</li>
                                            <li>30天獲利保證</li>
                                        </ul>
                                    </div>
                                    
                                    <form id="fakeInvestmentForm">
                                        <div class="mb-3">
                                            <label class="form-label">姓名 *</label>
                                            <input type="text" class="form-control" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">手機號碼 *</label>
                                            <input type="tel" class="form-control" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">電子郵件 *</label>
                                            <input type="email" class="form-control" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">初始投資金額 *</label>
                                            <select class="form-select" required>
                                                <option value="">請選擇</option>
                                                <option value="50000">NT$ 50,000 (推薦新手)</option>
                                                <option value="100000">NT$ 100,000 (熱門選擇)</option>
                                                <option value="500000">NT$ 500,000 (高獲利)</option>
                                                <option value="1000000">NT$ 1,000,000+ (VIP專案)</option>
                                            </select>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <small>
                                                <i class="fas fa-calculator me-2"></i>
                                                預估月獲利：<strong class="text-success">30-50%</strong><br>
                                                風險等級：<strong class="text-success">極低風險</strong>
                                            </small>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="agree" required>
                                            <label class="form-check-label" for="agree">
                                                <small>我同意投資條款並了解獲利保證</small>
                                            </label>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-warning btn-lg">
                                                <i class="fas fa-money-bill-wave me-2"></i>
                                                開始賺錢
                                            </button>
                                        </div>
                                    </form>
                                    
                                    <div class="text-center mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            資金安全保障 | 24小時客服
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-3 bg-success text-white">
                                <div class="card-body text-center">
                                    <h6><i class="fas fa-users me-2"></i>即時數據</h6>
                                    <p class="mb-1">線上投資者：<strong>8,547</strong></p>
                                    <p class="mb-1">今日新增：<strong>+234</strong></p>
                                    <p class="mb-0">平台總資產：<strong>$2.8億</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-light p-4">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 class="text-success">99.8%</h4>
                                <small>獲利成功率</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-success">30%+</h4>
                                <small>月平均獲利</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-success">10,000+</h4>
                                <small>成功投資者</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-success">24/7</h4>
                                <small>AI智能交易</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表單提交處理
        document.getElementById('fakeInvestmentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>處理中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                document.getElementById('warningOverlay').style.display = 'flex';
            }, 2000);
        });
        
        function goBackToSimulation() {
            window.location.href = '/scam-simulation';
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // 輸入提示
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (!this.hasAttribute('data-warned')) {
                    this.setAttribute('data-warned', 'true');
                    this.style.borderColor = '#dc3545';
                    
                    const tooltip = document.createElement('div');
                    tooltip.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    tooltip.style.top = '20px';
                    tooltip.style.right = '20px';
                    tooltip.style.zIndex = '1050';
                    tooltip.style.maxWidth = '300px';
                    tooltip.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>真實情況下絕對不要投資可疑平台！
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(tooltip);
                    
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.remove();
                        }
                    }, 5000);
                }
            });
        });
        
        // 動態更新數字效果
        function updateStats() {
            const onlineUsers = document.querySelector('.card.bg-success p:nth-child(2) strong');
            const newUsers = document.querySelector('.card.bg-success p:nth-child(3) strong');
            
            if (onlineUsers && newUsers) {
                let currentOnline = parseInt(onlineUsers.textContent.replace(',', ''));
                let currentNew = parseInt(newUsers.textContent.replace('+', ''));
                
                onlineUsers.textContent = (currentOnline + Math.floor(Math.random() * 10)).toLocaleString();
                newUsers.textContent = '+' + (currentNew + Math.floor(Math.random() * 5));
            }
        }
        
        setInterval(updateStats, 3000);
    </script>
</body>
</html>
