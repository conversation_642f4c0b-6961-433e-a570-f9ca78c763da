version: '3.8'

services:
  fraud-prevention-app:
    build: .
    container_name: fraud-prevention-platform
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - PORT=3001
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - fraud-prevention-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  fraud-prevention-network:
    driver: bridge

volumes:
  app-data:
    driver: local
