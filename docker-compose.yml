version: '3.8'

services:
  fraud-prevention-app:
    build: .
    container_name: fraud-prevention-platform
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - PORT=3000
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - fraud-prevention-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  fraud-prevention-network:
    driver: bridge

volumes:
  app-data:
    driver: local
