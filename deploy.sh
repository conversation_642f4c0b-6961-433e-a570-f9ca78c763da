#!/bin/bash

# 防詐騙教學平台部署腳本
# 適用於 Synology NAS

echo "🚀 開始部署防詐騙教學平台..."

# 檢查 Docker 是否安裝
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安裝，請先安裝 Docker 套件"
    exit 1
fi

# 檢查 docker-compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "⚠️  docker-compose 未找到，嘗試使用 docker compose"
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

# 創建必要的目錄
echo "📁 創建必要目錄..."
mkdir -p data logs

# 設定權限
echo "🔐 設定檔案權限..."
chmod +x deploy.sh
chmod +x healthcheck.js

# 停止現有容器（如果存在）
echo "🛑 停止現有容器..."
$COMPOSE_CMD down 2>/dev/null || true

# 構建並啟動容器
echo "🏗️  構建並啟動容器..."
$COMPOSE_CMD up -d --build

# 等待容器啟動
echo "⏳ 等待容器啟動..."
sleep 10

# 檢查容器狀態
if docker ps | grep -q "fraud-prevention-platform"; then
    echo "✅ 部署成功！"
    echo ""
    echo "🌐 訪問網址："
    echo "   內網：http://$(hostname -I | awk '{print $1}'):3000"
    echo "   或：http://localhost:3000"
    echo ""
    echo "📊 檢查狀態："
    echo "   docker ps"
    echo "   docker logs fraud-prevention-platform"
    echo ""
    echo "🔄 重新啟動："
    echo "   $COMPOSE_CMD restart"
    echo ""
    echo "🛑 停止服務："
    echo "   $COMPOSE_CMD down"
else
    echo "❌ 部署失敗，請檢查日誌："
    echo "   docker logs fraud-prevention-platform"
    exit 1
fi
