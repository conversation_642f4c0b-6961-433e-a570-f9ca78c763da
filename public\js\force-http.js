// 強制所有連結使用 HTTP 協議
document.addEventListener('DOMContentLoaded', function() {
    console.log('Force HTTP script loaded');
    
    // 獲取當前的 HTTP URL
    const currentHost = window.location.host;
    const httpBaseUrl = `http://${currentHost}`;
    
    console.log('HTTP Base URL:', httpBaseUrl);
    
    // 修復所有內部連結
    function fixLinks() {
        const links = document.querySelectorAll('a[href]');
        
        links.forEach(link => {
            const href = link.getAttribute('href');
            
            // 只處理相對路徑和內部連結
            if (href && (href.startsWith('/') || href.startsWith('./') || !href.includes('://'))) {
                const newHref = href.startsWith('/') ? `${httpBaseUrl}${href}` : `${httpBaseUrl}/${href}`;
                link.setAttribute('href', newHref);
                console.log(`Fixed link: ${href} -> ${newHref}`);
            }
        });
    }
    
    // 立即修復連結
    fixLinks();
    
    // 監聽所有點擊事件
    document.addEventListener('click', function(e) {
        const target = e.target.closest('a');
        
        if (target && target.href) {
            const href = target.href;
            
            // 如果是 HTTPS 內部連結，強制改為 HTTP
            if (href.startsWith('https://') && href.includes(currentHost)) {
                e.preventDefault();
                const httpUrl = href.replace('https://', 'http://');
                console.log(`Redirecting from HTTPS to HTTP: ${href} -> ${httpUrl}`);
                window.location.href = httpUrl;
                return false;
            }
        }
    });
    
    // 攔截表單提交
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.action && form.action.startsWith('https://') && form.action.includes(currentHost)) {
            form.action = form.action.replace('https://', 'http://');
            console.log('Fixed form action to HTTP:', form.action);
        }
    });
    
    // 監聽 window.location 變更
    const originalLocation = window.location;
    Object.defineProperty(window, 'location', {
        get: function() {
            return originalLocation;
        },
        set: function(url) {
            if (typeof url === 'string' && url.startsWith('https://') && url.includes(currentHost)) {
                url = url.replace('https://', 'http://');
                console.log('Intercepted location change to HTTP:', url);
            }
            originalLocation.href = url;
        }
    });
    
    // 定期檢查並修復新添加的連結
    setInterval(fixLinks, 1000);
});

// 攔截 fetch 請求
const originalFetch = window.fetch;
window.fetch = function(url, options) {
    if (typeof url === 'string' && url.startsWith('https://') && url.includes(window.location.host)) {
        url = url.replace('https://', 'http://');
        console.log('Fixed fetch URL to HTTP:', url);
    }
    return originalFetch.call(this, url, options);
};
