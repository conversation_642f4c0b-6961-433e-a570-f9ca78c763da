{"name": "fraud-prevention-platform", "version": "1.0.0", "description": "防詐騙 × 資安 × 金融常識 智能線上教學平台", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "restart": "pkill -f 'node server.js' && node server.js", "stop": "pkill -f 'node server.js'", "logs": "tail -f logs/app.log", "test": "jest"}, "keywords": ["education", "fraud-prevention", "cybersecurity", "financial-literacy"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.0", "node-fetch": "^2.7.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1"}}