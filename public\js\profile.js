document.addEventListener('DOMContentLoaded', function() {
    checkAuthAndLoadProfile();
    setupForms();
});

function checkAuthAndLoadProfile() {
    const token = localStorage.getItem('token');
    
    if (!token) {
        showLoginRequired();
        return;
    }
    
    loadProfileData();
}

function showLoginRequired() {
    document.getElementById('loading').classList.add('d-none');
    document.getElementById('login-required').classList.remove('d-none');
}

function showProfileContent() {
    document.getElementById('loading').classList.add('d-none');
    document.getElementById('login-required').classList.add('d-none');
    document.getElementById('profile-content').classList.remove('d-none');
}

async function loadProfileData() {
    try {
        const response = await apiRequest('/api/user/profile');
        
        if (response.error) {
            showAlert(response.error, 'danger');
            return;
        }
        
        displayProfileData(response.user);
        showProfileContent();
        
    } catch (error) {
        console.error('載入個人資料錯誤:', error);
        showAlert('載入個人資料失敗，請稍後再試', 'danger');
        showLoginRequired();
    }
}

function displayProfileData(user) {
    // 更新顯示區域
    document.getElementById('display-username').textContent = user.username;
    document.getElementById('display-email').textContent = user.email;
    document.getElementById('profile-username').textContent = user.username;
    document.getElementById('profile-email').textContent = user.email;
    document.getElementById('profile-created').textContent = formatDate(new Date(user.created_at));
    
    // 更新編輯表單
    document.getElementById('edit-username').value = user.username;
    document.getElementById('edit-email').value = user.email;
}

function setupForms() {
    // 編輯個人資料表單
    const editForm = document.getElementById('editProfileForm');
    if (editForm) {
        editForm.addEventListener('submit', handleProfileUpdate);
    }
    
    // 變更密碼表單
    const passwordForm = document.getElementById('changePasswordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', handlePasswordChange);
    }
}

function showEditForm() {
    document.getElementById('profile-display').classList.add('d-none');
    document.getElementById('profile-edit').classList.remove('d-none');
    document.getElementById('password-change').classList.add('d-none');
}

function showChangePasswordForm() {
    document.getElementById('profile-display').classList.add('d-none');
    document.getElementById('profile-edit').classList.add('d-none');
    document.getElementById('password-change').classList.remove('d-none');
}

function cancelEdit() {
    document.getElementById('profile-display').classList.remove('d-none');
    document.getElementById('profile-edit').classList.add('d-none');
    document.getElementById('password-change').classList.add('d-none');
}

function cancelPasswordChange() {
    document.getElementById('profile-display').classList.remove('d-none');
    document.getElementById('profile-edit').classList.add('d-none');
    document.getElementById('password-change').classList.add('d-none');
    
    // 清空密碼表單
    document.getElementById('changePasswordForm').reset();
}

async function handleProfileUpdate(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const updateData = {
        username: formData.get('username'),
        email: formData.get('email')
    };
    
    try {
        const response = await apiRequest('/api/user/profile', {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
        
        if (response.error) {
            showAlert(response.error, 'danger');
            return;
        }
        
        // 更新本地儲存的使用者名稱
        localStorage.setItem('username', updateData.username);
        
        // 重新載入資料
        await loadProfileData();
        
        // 返回顯示模式
        cancelEdit();
        
        showAlert('個人資料更新成功！', 'success');
        
    } catch (error) {
        console.error('更新個人資料錯誤:', error);
        showAlert('更新個人資料失敗，請稍後再試', 'danger');
    }
}

async function handlePasswordChange(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const currentPassword = formData.get('currentPassword');
    const newPassword = formData.get('newPassword');
    const confirmPassword = formData.get('confirmPassword');
    
    // 驗證新密碼
    if (newPassword !== confirmPassword) {
        showAlert('新密碼與確認密碼不符', 'danger');
        return;
    }
    
    if (newPassword.length < 8) {
        showAlert('新密碼至少需要8個字符', 'danger');
        return;
    }
    
    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
        showAlert('新密碼需包含大小寫字母和數字', 'danger');
        return;
    }
    
    try {
        const response = await apiRequest('/api/user/change-password', {
            method: 'PUT',
            body: JSON.stringify({
                currentPassword,
                newPassword
            })
        });
        
        if (response.error) {
            showAlert(response.error, 'danger');
            return;
        }
        
        // 返回顯示模式
        cancelPasswordChange();
        
        showAlert('密碼變更成功！', 'success');
        
    } catch (error) {
        console.error('變更密碼錯誤:', error);
        showAlert('變更密碼失敗，請稍後再試', 'danger');
    }
}

function formatDate(date) {
    return date.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}
