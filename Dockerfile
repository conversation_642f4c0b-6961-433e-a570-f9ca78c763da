# 使用官方 Node.js 18 LTS 版本
FROM node:18-alpine

# 安裝 wget (用於健康檢查)
RUN apk add --no-cache wget

# 設定工作目錄
WORKDIR /app

# 複製 package.json 和 package-lock.json
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production && npm cache clean --force

# 複製應用程式代碼
COPY . .

# 創建必要的目錄
RUN mkdir -p data logs

# 設定權限
RUN chown -R node:node /app

# 切換到非 root 用戶
USER node

# 暴露端口
EXPOSE 3000

# 啟動應用程式
CMD ["npm", "start"]
