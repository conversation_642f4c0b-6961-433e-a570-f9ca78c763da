const express = require('express');
const path = require('path');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// 生產環境日誌設定
if (process.env.NODE_ENV === 'production') {
    const fs = require('fs');
    const path = require('path');

    // 確保日誌目錄存在
    const logDir = path.join(__dirname, 'logs');
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }

    // 重定向 console.log 到文件
    const logFile = path.join(logDir, 'app.log');
    const logStream = fs.createWriteStream(logFile, { flags: 'a' });

    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    console.log = (...args) => {
        const timestamp = new Date().toISOString();
        logStream.write(`[${timestamp}] INFO: ${args.join(' ')}\n`);
        originalConsoleLog(...args);
    };

    console.error = (...args) => {
        const timestamp = new Date().toISOString();
        logStream.write(`[${timestamp}] ERROR: ${args.join(' ')}\n`);
        originalConsoleError(...args);
    };
}

// 記憶體資料庫模擬
let memoryDB = {
    users: [],
    questions: [],
    userQuizSessions: [],
    userProgress: []
};

let nextUserId = 1;
let nextQuestionId = 1;
let nextSessionId = 1;
let nextProgressId = 1;

const DATA_FILE = path.join(__dirname, 'data.json');

// 載入持久化資料
function loadData() {
    try {
        if (fs.existsSync(DATA_FILE)) {
            const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
            memoryDB = data.memoryDB || memoryDB;
            nextUserId = data.nextUserId || 1;
            nextQuestionId = data.nextQuestionId || 1;
            nextSessionId = data.nextSessionId || 1;
            nextProgressId = data.nextProgressId || 1;
            console.log(`載入持久化資料: ${memoryDB.users.length} 個使用者`);
        }
    } catch (error) {
        console.error('載入資料錯誤:', error);
    }
}

// 儲存資料到檔案
function saveData() {
    try {
        const data = {
            memoryDB,
            nextUserId,
            nextQuestionId,
            nextSessionId,
            nextProgressId
        };
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
        console.error('儲存資料錯誤:', error);
    }
}

// 完全禁用 Helmet 以避免任何 HTTPS 重定向
// app.use(helmet());

// 明確設定不使用 HTTPS 的標頭
app.use((req, res, next) => {
    // 移除任何可能導致 HTTPS 重定向的標頭
    res.removeHeader('Strict-Transport-Security');
    res.removeHeader('X-Forwarded-Proto');

    // 設定明確的 HTTP 標頭
    res.setHeader('X-Forwarded-Proto', 'http');
    next();
});

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: { error: '登入嘗試次數過多，請稍後再試' },
    standardHeaders: true,
    legacyHeaders: false,
});

const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: { error: '請求過於頻繁，請稍後再試' },
});

app.use('/api/auth', authLimiter);
app.use('/api', generalLimiter);

async function initDatabase() {
    try {
        console.log('使用記憶體資料庫模式');
        loadData(); // 載入持久化資料
        await seedQuestions();
        console.log('資料庫初始化完成');
        console.log(`目前使用者數量: ${memoryDB.users.length}`);
    } catch (error) {
        console.error('資料庫初始化錯誤:', error);
        process.exit(1);
    }
}

async function seedQuestions() {
    try {
        if (memoryDB.questions.length > 0) {
            console.log('題庫已存在，跳過初始化');
            return;
        }

        // 載入完整題庫
        const { questions } = require('./seed_questions.js');

        questions.forEach(question => {
            memoryDB.questions.push({
                id: nextQuestionId++,
                category: question.category,
                question_text: question.question_text,
                question_type: 'multiple_choice',
                options: question.options,
                correct_answer: question.correct_answer,
                explanation: question.explanation,
                difficulty: question.difficulty,
                created_at: new Date()
            });
        });

        console.log(`題庫初始化完成，共載入 ${memoryDB.questions.length} 道題目`);

        // 統計各類別題目數量
        const categories = ['fraud', 'cybersecurity', 'finance'];
        categories.forEach(category => {
            const count = memoryDB.questions.filter(q => q.category === category).length;
            console.log(`${category}: ${count} 題`);
        });

    } catch (error) {
        console.error('題庫初始化錯誤:', error);
    }
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: '需要登入' });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret', (err, user) => {
        if (err) {
            return res.status(403).json({ error: '無效的登入狀態' });
        }
        req.user = user;
        next();
    });
}

app.post('/api/auth/register', [
    body('username').isLength({ min: 3, max: 50 }).withMessage('使用者名稱長度需在3-50字符之間'),
    body('email').isEmail().withMessage('請輸入有效的電子郵件'),
    body('password').isLength({ min: 8 }).withMessage('密碼至少需要8個字符')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('密碼需包含大小寫字母和數字'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { username, email, password } = req.body;

        // 檢查使用者是否已存在
        const existingUser = memoryDB.users.find(u => u.username === username || u.email === email);
        if (existingUser) {
            return res.status(400).json({ error: '使用者名稱或電子郵件已存在' });
        }

        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        const newUser = {
            id: nextUserId++,
            username,
            email,
            password_hash: passwordHash,
            created_at: new Date(),
            updated_at: new Date(),
            is_active: true
        };

        memoryDB.users.push(newUser);
        saveData(); // 儲存資料

        const token = jwt.sign(
            { userId: newUser.id, username },
            process.env.JWT_SECRET || 'fallback-secret',
            { expiresIn: '24h' }
        );

        console.log('新使用者註冊:', { id: newUser.id, username, email });
        console.log('目前使用者總數:', memoryDB.users.length);

        res.status(201).json({
            message: '註冊成功',
            token,
            user: { id: newUser.id, username, email }
        });
    } catch (error) {
        console.error('註冊錯誤:', error);
        res.status(500).json({ error: '註冊失敗，請稍後再試' });
    }
});

app.post('/api/auth/login', [
    body('email').isEmail().withMessage('請輸入有效的電子郵件'),
    body('password').notEmpty().withMessage('請輸入密碼'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { email, password } = req.body;

        console.log('登入嘗試:', email);
        console.log('目前資料庫中的使用者:', memoryDB.users.map(u => ({ id: u.id, email: u.email, username: u.username })));

        const user = memoryDB.users.find(u => u.email === email);

        if (!user) {
            console.log('找不到使用者:', email);
            return res.status(401).json({ error: '電子郵件或密碼錯誤' });
        }

        if (!user.is_active) {
            return res.status(401).json({ error: '帳號已被停用' });
        }

        const isValidPassword = await bcrypt.compare(password, user.password_hash);

        if (!isValidPassword) {
            return res.status(401).json({ error: '電子郵件或密碼錯誤' });
        }

        const token = jwt.sign(
            { userId: user.id, username: user.username },
            process.env.JWT_SECRET || 'fallback-secret',
            { expiresIn: '24h' }
        );

        res.json({
            message: '登入成功',
            token,
            user: { id: user.id, username: user.username, email: user.email }
        });
    } catch (error) {
        console.error('登入錯誤:', error);
        res.status(500).json({ error: '登入失敗，請稍後再試' });
    }
});

app.get('/api/questions/:category', async (req, res) => {
    try {
        const { category } = req.params;
        const validCategories = ['fraud', 'cybersecurity', 'finance'];

        if (!validCategories.includes(category)) {
            return res.status(400).json({ error: '無效的題目類別' });
        }

        // 從記憶體資料庫中取得該類別的題目
        const categoryQuestions = memoryDB.questions.filter(q => q.category === category);

        // 隨機選取10道題目
        const shuffled = categoryQuestions.sort(() => 0.5 - Math.random());
        const selectedQuestions = shuffled.slice(0, 10).map(q => ({
            id: q.id,
            question_text: q.question_text,
            options: q.options,
            question_type: q.question_type,
            difficulty: q.difficulty
        }));

        res.json(selectedQuestions);
    } catch (error) {
        console.error('獲取題目錯誤:', error);
        res.status(500).json({ error: '獲取題目失敗' });
    }
});

app.post('/api/quiz/submit', authenticateToken, async (req, res) => {
    try {
        const { category, answers } = req.body;
        const userId = req.user.userId;

        if (!category || !answers || !Array.isArray(answers)) {
            return res.status(400).json({ error: '無效的提交資料' });
        }

        const questionIds = answers.map(a => a.questionId);
        const questions = memoryDB.questions.filter(q => questionIds.includes(q.id));

        let correctCount = 0;
        const results = answers.map(answer => {
            const question = questions.find(q => q.id === answer.questionId);
            const isCorrect = question && question.correct_answer === answer.selectedAnswer;
            if (isCorrect) correctCount++;

            return {
                questionId: answer.questionId,
                selectedAnswer: answer.selectedAnswer,
                correctAnswer: question?.correct_answer,
                isCorrect,
                explanation: question?.explanation
            };
        });

        const score = Math.round((correctCount / answers.length) * 100);

        // 儲存測驗記錄
        const newSession = {
            id: nextSessionId++,
            user_id: userId,
            category,
            score,
            total_questions: answers.length,
            answers: JSON.stringify(results),
            completed_at: new Date()
        };
        memoryDB.userQuizSessions.push(newSession);

        // 更新使用者進度
        let userProgress = memoryDB.userProgress.find(p => p.user_id === userId && p.category === category);
        if (userProgress) {
            userProgress.best_score = Math.max(userProgress.best_score, score);
            userProgress.total_attempts += 1;
            userProgress.last_attempt_at = new Date();
        } else {
            memoryDB.userProgress.push({
                id: nextProgressId++,
                user_id: userId,
                category,
                best_score: score,
                total_attempts: 1,
                last_attempt_at: new Date()
            });
        }

        saveData(); // 儲存資料

        res.json({
            score,
            correctCount,
            totalQuestions: answers.length,
            results
        });
    } catch (error) {
        console.error('提交測驗錯誤:', error);
        res.status(500).json({ error: '提交測驗失敗' });
    }
});

app.get('/api/progress', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        const progress = memoryDB.userProgress.filter(p => p.user_id === userId);
        const recentSessions = memoryDB.userQuizSessions
            .filter(s => s.user_id === userId)
            .sort((a, b) => new Date(b.completed_at) - new Date(a.completed_at))
            .slice(0, 10);

        res.json({
            progress,
            recentSessions
        });
    } catch (error) {
        console.error('獲取進度錯誤:', error);
        res.status(500).json({ error: '獲取進度失敗' });
    }
});

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'index.html'));
});

app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'login.html'));
});

app.get('/register', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'register.html'));
});

app.get('/quiz', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'quiz.html'));
});

app.get('/quiz/:category', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'quiz.html'));
});

app.get('/progress', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'progress.html'));
});

app.get('/profile', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'profile.html'));
});

// 詐騙模擬頁面路由
app.get('/scam-simulation', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-simulation.html'));
});

app.get('/scam-simulation/phishing-bank', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-phishing-bank.html'));
});

app.get('/scam-simulation/fake-shopping', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-fake-shopping.html'));
});

app.get('/scam-simulation/fake-lottery', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-fake-lottery.html'));
});

app.get('/scam-simulation/fake-investment', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-fake-investment.html'));
});

app.get('/scam-simulation/fake-support', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-fake-support.html'));
});

app.get('/scam-simulation/romance-scam', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'scam-romance.html'));
});

// 調試用 API - 列出所有使用者（僅開發環境）
app.get('/api/debug/users', (req, res) => {
    if (process.env.NODE_ENV === 'production') {
        return res.status(404).json({ error: '此端點僅在開發環境可用' });
    }

    const users = memoryDB.users.map(u => ({
        id: u.id,
        username: u.username,
        email: u.email,
        created_at: u.created_at
    }));

    res.json({
        total: users.length,
        users: users
    });
});

// API 路由：取得使用者個人資料
app.get('/api/user/profile', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        const user = memoryDB.users.find(u => u.id === userId);

        if (!user) {
            return res.status(404).json({ error: '使用者不存在' });
        }

        res.json({
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                created_at: user.created_at
            }
        });
    } catch (error) {
        console.error('取得個人資料錯誤:', error);
        res.status(500).json({ error: '取得個人資料失敗' });
    }
});

// API 路由：更新使用者個人資料
app.put('/api/user/profile', authenticateToken, [
    body('username').isLength({ min: 3, max: 50 }).withMessage('使用者名稱長度需在3-50字符之間'),
    body('email').isEmail().withMessage('請輸入有效的電子郵件'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const userId = req.user.userId;
        const { username, email } = req.body;

        // 檢查使用者名稱和電子郵件是否已被其他人使用
        const existingUser = memoryDB.users.find(u =>
            (u.username === username || u.email === email) && u.id !== userId
        );

        if (existingUser) {
            return res.status(400).json({ error: '使用者名稱或電子郵件已被使用' });
        }

        // 更新使用者資料
        const user = memoryDB.users.find(u => u.id === userId);
        if (user) {
            user.username = username;
            user.email = email;
            user.updated_at = new Date();
        }

        res.json({ message: '個人資料更新成功' });
    } catch (error) {
        console.error('更新個人資料錯誤:', error);
        res.status(500).json({ error: '更新個人資料失敗' });
    }
});

// API 路由：變更密碼
app.put('/api/user/change-password', authenticateToken, [
    body('currentPassword').notEmpty().withMessage('請輸入目前密碼'),
    body('newPassword').isLength({ min: 8 }).withMessage('新密碼至少需要8個字符')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('新密碼需包含大小寫字母和數字'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const userId = req.user.userId;
        const { currentPassword, newPassword } = req.body;

        // 驗證目前密碼
        const user = memoryDB.users.find(u => u.id === userId);

        if (!user) {
            return res.status(404).json({ error: '使用者不存在' });
        }

        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);

        if (!isValidPassword) {
            return res.status(400).json({ error: '目前密碼錯誤' });
        }

        // 更新密碼
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

        user.password_hash = newPasswordHash;
        user.updated_at = new Date();

        res.json({ message: '密碼變更成功' });
    } catch (error) {
        console.error('變更密碼錯誤:', error);
        res.status(500).json({ error: '變更密碼失敗' });
    }
});

app.use((req, res) => {
    res.status(404).json({ error: '頁面不存在' });
});

app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: '伺服器內部錯誤' });
});

// 優雅關閉處理
process.on('SIGTERM', () => {
    console.log('收到 SIGTERM 信號，正在關閉伺服器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到 SIGINT 信號，正在關閉伺服器...');
    process.exit(0);
});

// 未捕獲的異常處理
process.on('uncaughtException', (error) => {
    console.error('未捕獲的異常:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未處理的 Promise 拒絕:', reason);
    process.exit(1);
});

initDatabase().then(() => {
    const server = app.listen(PORT, '0.0.0.0', () => {
        console.log(`伺服器運行在 http://0.0.0.0:${PORT}`);
        console.log(`內網訪問: http://localhost:${PORT}`);
        console.log(`環境: ${process.env.NODE_ENV || 'development'}`);
        console.log(`PID: ${process.pid}`);

        // 嘗試獲取本機 IP 地址
        const os = require('os');
        const networkInterfaces = os.networkInterfaces();
        const addresses = [];

        for (const interfaceName in networkInterfaces) {
            const networkInterface = networkInterfaces[interfaceName];
            for (const network of networkInterface) {
                if (network.family === 'IPv4' && !network.internal) {
                    addresses.push(network.address);
                }
            }
        }

        if (addresses.length > 0) {
            addresses.forEach(address => {
                console.log(`網路訪問: http://${address}:${PORT}`);
            });
        }
    });

    // 設定伺服器超時
    server.timeout = 30000;
}).catch((error) => {
    console.error('伺服器啟動失敗:', error);
    process.exit(1);
});