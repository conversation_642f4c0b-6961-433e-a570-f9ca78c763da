# 📋 部署檢查清單

## 🎯 部署前準備

### Synology NAS 檢查
- [ ] DSM 版本 7.0 或更新
- [ ] 可用空間至少 500MB
- [ ] 網路連接正常
- [ ] 管理員權限

### 套件安裝
- [ ] Web Station 套件已安裝
- [ ] Node.js v18 (或更新版本) 已安裝
- [ ] SSH 服務已啟用 (控制台 → 終端機和 SNMP)

### 文件準備
- [ ] 所有專案文件已下載
- [ ] 文件完整性檢查通過

## 📁 文件上傳檢查

### 必要文件
- [ ] `server.js` - 主伺服器文件
- [ ] `package.json` - 專案配置
- [ ] `seed_questions.js` - 題庫資料
- [ ] `webstation-setup.sh` - 設定腳本

### 資料夾結構
```
/web/fraud-prevention-platform/
├── [ ] server.js
├── [ ] package.json  
├── [ ] seed_questions.js
├── [ ] webstation-setup.sh
├── [ ] views/ (所有 HTML 文件)
├── [ ] public/css/ (樣式文件)
├── [ ] public/js/ (JavaScript 文件)
└── [ ] data/ (會自動創建)
```

### HTML 文件檢查
- [ ] `index.html` - 首頁
- [ ] `login.html` - 登入頁面
- [ ] `register.html` - 註冊頁面
- [ ] `quiz.html` - 測驗頁面
- [ ] `progress.html` - 進度頁面
- [ ] `profile.html` - 個人資料頁面
- [ ] `scam-simulation.html` - 詐騙模擬主頁
- [ ] `scam-phishing-bank.html` - 假銀行頁面
- [ ] `scam-fake-shopping.html` - 假購物頁面
- [ ] `scam-fake-lottery.html` - 假中獎頁面
- [ ] `scam-fake-investment.html` - 假投資頁面
- [ ] `scam-fake-support.html` - 假客服頁面
- [ ] `scam-romance.html` - 感情詐騙頁面

### CSS/JS 文件檢查
- [ ] `public/css/style.css`
- [ ] `public/js/main.js`
- [ ] `public/js/auth.js`
- [ ] `public/js/quiz.js`
- [ ] `public/js/scam-simulation.js`

## ⚙️ 設定檢查

### SSH 連接測試
- [ ] 能夠 SSH 連接到 NAS
- [ ] 能夠切換到專案目錄
- [ ] 有執行權限

### 依賴安裝
- [ ] `npm install` 執行成功
- [ ] 所有依賴包安裝完成
- [ ] 沒有錯誤訊息

### 環境設定
- [ ] `.env` 文件已創建
- [ ] `JWT_SECRET` 已設定
- [ ] `NODE_ENV=production`
- [ ] `PORT=3001`

## 🌐 Web Station 設定

### 虛擬主機創建
- [ ] 主機名稱：`fraud-prevention`
- [ ] 連接埠：`3001`
- [ ] 文件根目錄：`/web/fraud-prevention-platform`
- [ ] 後端伺服器：已啟用
- [ ] 類型：`Node.js`
- [ ] 啟動檔案：`server.js`

### 環境變數設定
- [ ] `NODE_ENV=production`
- [ ] `JWT_SECRET=你的密鑰`
- [ ] `PORT=3001`

### 虛擬主機啟動
- [ ] 虛擬主機已啟用
- [ ] 狀態顯示「正常」
- [ ] 沒有錯誤訊息

## 🔍 功能測試

### 基本訪問測試
- [ ] 能夠訪問 `http://NAS_IP:3001`
- [ ] 首頁正常載入
- [ ] 所有 CSS 樣式正常
- [ ] JavaScript 功能正常

### 用戶功能測試
- [ ] 註冊功能正常
- [ ] 登入功能正常
- [ ] 登出功能正常

### 測驗功能測試
- [ ] 防詐騙測驗可以開始
- [ ] 題目正常顯示
- [ ] 答案選擇正常
- [ ] 結果頁面正常

### 詐騙模擬測試
- [ ] 詐騙模擬主頁可訪問
- [ ] 各種詐騙模擬頁面正常
- [ ] 警告機制正常觸發
- [ ] 教育內容正常顯示

### 進度追蹤測試
- [ ] 學習進度頁面正常
- [ ] 個人資料頁面正常
- [ ] 資料持久化正常

## 🛡️ 安全設定檢查

### 防火牆設定
- [ ] 防火牆已啟用
- [ ] 連接埠 3001 已開放
- [ ] 存取控制已設定（如需要）

### SSL 設定（可選）
- [ ] SSL 憑證已申請
- [ ] 憑證已套用到虛擬主機
- [ ] HTTPS 訪問正常

### 存取限制（可選）
- [ ] IP 白名單已設定
- [ ] 使用者存取控制已設定

## 📊 效能檢查

### 系統資源
- [ ] CPU 使用率正常 (<80%)
- [ ] 記憶體使用率正常 (<80%)
- [ ] 磁碟空間充足 (>100MB 可用)

### 應用程式效能
- [ ] 頁面載入速度正常 (<3秒)
- [ ] 測驗回應速度正常
- [ ] 沒有記憶體洩漏

## 🔧 維護設定

### 日誌設定
- [ ] 日誌目錄已創建
- [ ] 日誌輪轉已設定
- [ ] 日誌權限正確

### 備份設定
- [ ] 資料備份計劃已制定
- [ ] 設定檔備份已完成

### 監控設定
- [ ] 系統監控已設定
- [ ] 異常警報已設定（如需要）

## 🌍 外網訪問設定（可選）

### 路由器設定
- [ ] 連接埠轉發已設定
- [ ] 外部連接埠已選擇
- [ ] 防火牆規則已更新

### DDNS 設定
- [ ] DDNS 服務已設定
- [ ] 域名解析正常
- [ ] 外網訪問測試通過

## ✅ 最終檢查

### 功能完整性
- [ ] 所有主要功能正常運作
- [ ] 使用者體驗良好
- [ ] 沒有明顯錯誤

### 文檔完整性
- [ ] 部署文檔已保存
- [ ] 設定資訊已記錄
- [ ] 維護指南已準備

### 用戶準備
- [ ] 使用說明已準備
- [ ] 訪問資訊已提供
- [ ] 支援聯絡方式已設定

---

## 🎉 部署完成！

當所有項目都勾選完成後，您的防詐騙教學平台就成功部署了！

### 下一步：
1. 📢 通知使用者訪問網址
2. 📊 監控系統運行狀況
3. 🔄 定期維護和更新
4. 📈 收集使用者反饋

### 緊急聯絡：
- 系統管理員：[您的聯絡方式]
- 技術支援：[支援管道]
