<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台灣第一銀行 - 網路銀行登入 (詐騙模擬)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .fake-bank-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .fake-header {
            background: #1e3c72;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .fake-logo {
            font-size: 24px;
            font-weight: bold;
        }
        .login-form {
            padding: 40px;
        }
        .form-control {
            border-radius: 25px;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
        }
        .btn-fake-login {
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            width: 100%;
        }
        .security-notice {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(220, 53, 69, 0.95);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .warning-content {
            max-width: 600px;
            padding: 40px;
            background: white;
            color: #333;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .pulse-animation {
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <!-- 詐騙警告覆蓋層 -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-4x pulse-animation"></i>
            </div>
            <h2 class="text-danger mb-3">🚨 這是詐騙網站！🚨</h2>
            <div class="alert alert-danger">
                <h5>您剛才差點成為詐騙受害者！</h5>
                <p class="mb-0">幸好這只是教育模擬，但在真實情況下，您的帳號密碼已經被竊取了！</p>
            </div>
            
            <div class="text-start mt-4">
                <h6 class="text-danger">這個假網站的詐騙特徵：</h6>
                <ul class="text-start">
                    <li>網址不是銀行官方網址（真實網址應該是 https://www.firstbank.com.tw）</li>
                    <li>要求輸入完整的帳號和密碼</li>
                    <li>可能透過釣魚郵件或簡訊連結進入</li>
                    <li>頁面設計模仿真實銀行但有細微差異</li>
                </ul>
                
                <h6 class="text-success">如何避免被騙：</h6>
                <ul class="text-start">
                    <li>✅ 直接輸入銀行官方網址，不要點擊連結</li>
                    <li>✅ 檢查網址列是否為正確的銀行網址</li>
                    <li>✅ 銀行不會透過郵件要求輸入密碼</li>
                    <li>✅ 使用銀行官方 APP 進行操作</li>
                    <li>✅ 發現可疑立即聯繫銀行客服</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="goBackToSimulation()">
                    <i class="fas fa-graduation-cap me-2"></i>我學會了
                </button>
                <button class="btn btn-primary btn-lg" onclick="goHome()">
                    <i class="fas fa-home me-2"></i>回到首頁
                </button>
            </div>
        </div>
    </div>

    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="fake-bank-container">
                    <div class="fake-header">
                        <div class="fake-logo">
                            <i class="fas fa-university me-2"></i>
                            台灣第一銀行
                        </div>
                        <p class="mb-0 mt-2">安全可靠的網路銀行服務</p>
                    </div>
                    
                    <div class="login-form">
                        <h4 class="text-center mb-4">網路銀行登入</h4>
                        
                        <div class="security-notice">
                            <i class="fas fa-shield-alt text-success me-2"></i>
                            <small>為了您的帳戶安全，請確認網址正確後再輸入資料</small>
                        </div>
                        
                        <form id="fakeLoginForm">
                            <div class="mb-3">
                                <label for="accountNumber" class="form-label">
                                    <i class="fas fa-user me-2"></i>帳號
                                </label>
                                <input type="text" class="form-control" id="accountNumber" 
                                       placeholder="請輸入您的銀行帳號" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>密碼
                                </label>
                                <input type="password" class="form-control" id="password" 
                                       placeholder="請輸入您的網路銀行密碼" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="securityCode" class="form-label">
                                    <i class="fas fa-key me-2"></i>安全碼
                                </label>
                                <input type="text" class="form-control" id="securityCode" 
                                       placeholder="請輸入簡訊驗證碼" required>
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    記住我的帳號
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-fake-login">
                                <i class="fas fa-sign-in-alt me-2"></i>安全登入
                            </button>
                        </form>
                        
                        <div class="text-center mt-4">
                            <a href="#" class="text-decoration-none">忘記密碼？</a>
                            <span class="mx-2">|</span>
                            <a href="#" class="text-decoration-none">申請網路銀行</a>
                        </div>
                        
                        <div class="mt-4 p-3 bg-light rounded">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                注意事項：請勿在公共場所使用網路銀行，離開時請記得登出。
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-white">
                        <i class="fas fa-lock me-1"></i>
                        256位元SSL加密保護 | 24小時客服專線：(02)2181-1111
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('fakeLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 模擬載入
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登入中...';
            submitBtn.disabled = true;
            
            // 2秒後顯示詐騙警告
            setTimeout(() => {
                document.getElementById('warningOverlay').style.display = 'flex';
            }, 2000);
        });
        
        function goBackToSimulation() {
            window.location.href = '/scam-simulation';
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // 監聽輸入事件，當使用者開始輸入時給予提示
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (!this.hasAttribute('data-warned')) {
                    this.setAttribute('data-warned', 'true');
                    this.style.borderColor = '#dc3545';
                    
                    // 顯示浮動提示
                    const tooltip = document.createElement('div');
                    tooltip.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    tooltip.style.top = '20px';
                    tooltip.style.right = '20px';
                    tooltip.style.zIndex = '1050';
                    tooltip.style.maxWidth = '300px';
                    tooltip.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>提醒：</strong>真實情況下絕對不要在可疑網站輸入個人資料！
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(tooltip);
                    
                    // 5秒後自動消失
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.remove();
                        }
                    }, 5000);
                }
            });
        });
    </script>
</body>
</html>
