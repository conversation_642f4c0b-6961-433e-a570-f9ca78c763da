let currentQuiz = {
    category: '',
    questions: [],
    currentQuestionIndex: 0,
    userAnswers: [],
    startTime: null
};

document.addEventListener('DOMContentLoaded', function() {
    const urlPath = window.location.pathname;
    const categoryMatch = urlPath.match(/\/quiz\/(.+)/);

    if (categoryMatch) {
        const category = categoryMatch[1];
        startQuiz(category);
    }

    // 設定按鈕事件監聽器
    setupButtonListeners();
});

function setupButtonListeners() {
    // 設定測驗選擇卡片
    const quizCards = document.querySelectorAll('.quiz-card');
    quizCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            if (category) {
                startQuiz(category);
            }
        });
    });

    // 設定上一題按鈕
    const prevBtn = document.getElementById('prev-btn');
    if (prevBtn) {
        prevBtn.addEventListener('click', previousQuestion);
    }

    // 設定下一題按鈕
    const nextBtn = document.getElementById('next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', nextQuestion);
    }

    // 設定結果頁面按鈕
    const restartBtn = document.getElementById('restart-btn');
    if (restartBtn) {
        restartBtn.addEventListener('click', restartQuiz);
    }

    const progressBtn = document.getElementById('progress-btn');
    if (progressBtn) {
        progressBtn.addEventListener('click', viewProgress);
    }
}

async function startQuiz(category) {
    const validCategories = {
        'fraud': '防詐騙',
        'cybersecurity': '資安',
        'finance': '金融常識'
    };
    
    if (!validCategories[category]) {
        alert('無效的測驗類型');
        return;
    }
    
    currentQuiz.category = category;
    currentQuiz.startTime = new Date();
    
    document.getElementById('quiz-selection').classList.add('d-none');
    document.getElementById('loading').classList.add('show');
    
    try {
        const response = await fetch(`/api/questions/${category}`);
        const data = await response.json();
        
        if (response.ok) {
            currentQuiz.questions = data;
            currentQuiz.userAnswers = new Array(data.length).fill(null);
            
            document.getElementById('loading').classList.remove('show');
            document.getElementById('quiz-content').classList.remove('d-none');
            document.getElementById('quiz-type-display').textContent = validCategories[category] + '測驗';
            
            showQuestion(0);
        } else {
            throw new Error(data.error || '載入題目失敗');
        }
    } catch (error) {
        console.error('載入題目錯誤:', error);
        document.getElementById('loading').classList.remove('show');
        alert('載入題目失敗，請稍後再試');
        document.getElementById('quiz-selection').classList.remove('d-none');
    }
}

function showQuestion(index) {
    // 更新當前題目索引
    currentQuiz.currentQuestionIndex = index;

    const question = currentQuiz.questions[index];
    const totalQuestions = currentQuiz.questions.length;

    document.getElementById('question-counter').textContent = `題目 ${index + 1} / ${totalQuestions}`;
    document.getElementById('progress-bar').style.width = `${((index + 1) / totalQuestions) * 100}%`;
    document.getElementById('question-text').textContent = question.question_text;

    const optionsContainer = document.getElementById('question-options');
    const options = JSON.parse(question.options);

    optionsContainer.innerHTML = '';

    options.forEach((option, optionIndex) => {
        const button = document.createElement('button');
        button.className = 'btn option-btn';
        button.textContent = option;
        button.onclick = () => selectOption(index, option, button);

        if (currentQuiz.userAnswers[index] === option) {
            button.classList.add('selected');
        }

        optionsContainer.appendChild(button);
    });

    // 更新按鈕狀態
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');

    prevBtn.disabled = index === 0;
    nextBtn.disabled = !currentQuiz.userAnswers[index];

    if (index === totalQuestions - 1) {
        nextBtn.innerHTML = '完成測驗<i class="fas fa-check ms-2"></i>';
    } else {
        nextBtn.innerHTML = '下一題<i class="fas fa-chevron-right ms-2"></i>';
    }

    document.getElementById('explanation').classList.add('d-none');
}

function selectOption(questionIndex, selectedOption, buttonElement) {
    console.log('Option selected:', selectedOption, 'for question:', questionIndex);

    const optionButtons = document.querySelectorAll('.option-btn');
    optionButtons.forEach(btn => btn.classList.remove('selected'));

    buttonElement.classList.add('selected');
    currentQuiz.userAnswers[questionIndex] = selectedOption;

    const nextBtn = document.getElementById('next-btn');
    nextBtn.disabled = false;

    console.log('Next button enabled, current answers:', currentQuiz.userAnswers);
}

function previousQuestion() {
    console.log('Previous question clicked, current index:', currentQuiz.currentQuestionIndex);
    if (currentQuiz.currentQuestionIndex > 0) {
        showQuestion(currentQuiz.currentQuestionIndex - 1);
    }
}

function nextQuestion() {
    console.log('Next question clicked, current index:', currentQuiz.currentQuestionIndex);
    const totalQuestions = currentQuiz.questions.length;

    if (currentQuiz.currentQuestionIndex < totalQuestions - 1) {
        showQuestion(currentQuiz.currentQuestionIndex + 1);
    } else {
        submitQuiz();
    }
}

async function submitQuiz() {
    const token = localStorage.getItem('token');
    
    if (!token) {
        if (confirm('需要登入才能保存測驗結果。是否要登入？')) {
            window.location.href = '/login';
            return;
        }
    }
    
    const answers = currentQuiz.questions.map((question, index) => ({
        questionId: question.id,
        selectedAnswer: currentQuiz.userAnswers[index]
    }));
    
    const hasUnanswered = answers.some(answer => !answer.selectedAnswer);
    if (hasUnanswered) {
        if (!confirm('還有題目未作答，確定要提交嗎？')) {
            return;
        }
    }
    
    try {
        document.getElementById('quiz-content').classList.add('d-none');
        document.getElementById('loading').classList.add('show');
        
        let results;
        
        if (token) {
            const response = await fetch('/api/quiz/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    category: currentQuiz.category,
                    answers: answers
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                results = data;
            } else {
                throw new Error(data.error || '提交失敗');
            }
        } else {
            results = calculateLocalResults(answers);
        }
        
        showResults(results);
        
    } catch (error) {
        console.error('提交測驗錯誤:', error);
        alert('提交測驗失敗，請稍後再試');
        document.getElementById('loading').classList.remove('show');
        document.getElementById('quiz-content').classList.remove('d-none');
    }
}

function calculateLocalResults(answers) {
    let correctCount = 0;
    const results = answers.map(answer => {
        const question = currentQuiz.questions.find(q => q.id === answer.questionId);
        const isCorrect = question && question.correct_answer === answer.selectedAnswer;
        if (isCorrect) correctCount++;
        
        return {
            questionId: answer.questionId,
            selectedAnswer: answer.selectedAnswer,
            correctAnswer: question?.correct_answer,
            isCorrect,
            explanation: question?.explanation
        };
    });
    
    const score = Math.round((correctCount / answers.length) * 100);
    
    return {
        score,
        correctCount,
        totalQuestions: answers.length,
        results
    };
}

function showResults(results) {
    document.getElementById('loading').classList.remove('show');
    document.getElementById('quiz-results').classList.remove('d-none');
    
    const { score, correctCount, totalQuestions } = results;
    
    document.getElementById('final-score').textContent = score + '%';
    document.getElementById('correct-count').textContent = correctCount;
    document.getElementById('incorrect-count').textContent = totalQuestions - correctCount;
    document.getElementById('total-questions').textContent = totalQuestions;
    document.getElementById('accuracy').textContent = score + '%';
    
    let message = '';
    if (score >= 90) {
        message = '優秀！你的安全意識很高！';
    } else if (score >= 70) {
        message = '不錯！但還有進步空間。';
    } else if (score >= 50) {
        message = '及格了！建議多加練習。';
    } else {
        message = '需要加強！請多學習相關知識。';
    }
    
    document.getElementById('score-message').textContent = message;
}

function restartQuiz() {
    currentQuiz = {
        category: '',
        questions: [],
        currentQuestionIndex: 0,
        userAnswers: [],
        startTime: null
    };
    
    document.getElementById('quiz-results').classList.add('d-none');
    document.getElementById('quiz-selection').classList.remove('d-none');
}

function viewProgress() {
    window.location.href = '/progress';
}