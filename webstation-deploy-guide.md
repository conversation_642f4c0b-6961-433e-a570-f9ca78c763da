# 防詐騙教學平台 - Web Station 部署指南

## 📋 系統需求

- Synology DS223j (已確認)
- DSM 7.0 或更新版本
- Web Station 套件
- Node.js 套件
- 至少 500MB 可用空間

## 🚀 部署步驟

### 步驟 1: 安裝必要套件

1. **安裝 Web Station**
   - 登入 Synology DSM
   - 開啟「套件中心」
   - 搜尋並安裝「Web Station」套件

2. **安裝 Node.js**
   - 在套件中心搜尋「Node.js v18」或「Node.js v20」
   - 安裝最新版本的 Node.js 套件

### 步驟 2: 準備專案文件

1. **創建網站資料夾**
   - 開啟「File Station」
   - 導航到 `/web` 資料夾
   - 創建新資料夾：`fraud-prevention-platform`

2. **上傳專案文件**
   將以下文件上傳到 `/web/fraud-prevention-platform/` 資料夾：
   ```
   fraud-prevention-platform/
   ├── server.js
   ├── package.json
   ├── seed_questions.js
   ├── views/
   │   ├── index.html
   │   ├── login.html
   │   ├── register.html
   │   ├── quiz.html
   │   ├── progress.html
   │   ├── profile.html
   │   ├── scam-simulation.html
   │   └── scam-*.html (所有詐騙模擬頁面)
   ├── public/
   │   ├── css/
   │   │   └── style.css
   │   └── js/
   │       ├── main.js
   │       ├── auth.js
   │       ├── quiz.js
   │       └── scam-simulation.js
   └── data/ (會自動創建)
   ```

### 步驟 3: 安裝 Node.js 依賴

1. **啟用 SSH 服務**
   - 控制台 → 終端機和 SNMP → 啟用 SSH 服務

2. **透過 SSH 連接**
   ```bash
   ssh admin@你的NAS_IP地址
   ```

3. **安裝專案依賴**
   ```bash
   cd /volume1/web/fraud-prevention-platform
   npm install
   ```

### 步驟 4: 設定 Web Station

1. **開啟 Web Station**
   - 在 DSM 中開啟 Web Station 套件

2. **創建虛擬主機**
   - 點選「虛擬主機」標籤
   - 點選「建立」→「建立虛擬主機」

3. **虛擬主機設定**
   ```
   基本設定：
   - 主機名稱：fraud-prevention
   - 連接埠：選擇可用連接埠（如 3001）
   - 文件根目錄：/web/fraud-prevention-platform
   
   後端伺服器：
   - 啟用後端伺服器：✓
   - 類型：Node.js
   - 啟動檔案：server.js
   - 環境：production
   ```

4. **進階設定**
   ```
   環境變數：
   - NODE_ENV=production
   - JWT_SECRET=your-super-secret-jwt-key-change-this
   - PORT=3001 (與虛擬主機連接埠一致)
   ```

### 步驟 5: 啟動服務

1. **啟動虛擬主機**
   - 在虛擬主機列表中找到 `fraud-prevention`
   - 點選「啟用」

2. **檢查狀態**
   - 狀態應顯示為「正常」
   - 如果顯示錯誤，檢查日誌文件

### 步驟 6: 訪問網站

1. **內網訪問**
   ```
   http://NAS_IP:3001
   例如：http://*************:3001
   ```

2. **設定域名（可選）**
   - 在虛擬主機設定中添加自定義域名
   - 修改本地 hosts 文件或 DNS 設定

## 🔧 進階設定

### SSL 憑證設定

1. **申請 SSL 憑證**
   - 控制台 → 安全性 → 憑證
   - 新增憑證 → Let's Encrypt

2. **套用到虛擬主機**
   - Web Station → 虛擬主機 → 編輯
   - HTTPS 設定 → 選擇憑證

### 反向代理設定（可選）

如果想使用標準 HTTP 連接埠 (80/443)：

1. **設定反向代理**
   - 控制台 → 應用程式入口網站 → 反向代理
   - 建立新規則

2. **代理設定**
   ```
   來源：
   - 協定：HTTP/HTTPS
   - 主機名稱：your-domain.com
   - 連接埠：80/443
   
   目的地：
   - 協定：HTTP
   - 主機名稱：localhost
   - 連接埠：3001
   ```

## 🛡️ 安全設定

### 防火牆設定

1. **開啟防火牆**
   - 控制台 → 安全性 → 防火牆
   - 啟用防火牆

2. **新增規則**
   ```
   規則名稱：Fraud Prevention Platform
   連接埠：3001
   協定：TCP
   來源 IP：允許的 IP 範圍
   ```

### 存取控制

1. **限制存取**
   - Web Station → 虛擬主機 → 編輯
   - 存取控制 → 設定允許的 IP

2. **設定 HTTPS 重定向**
   - 強制使用 HTTPS 連接

## 📊 監控和維護

### 檢查服務狀態

1. **Web Station 監控**
   - Web Station → 虛擬主機
   - 查看狀態和資源使用情況

2. **日誌檢查**
   ```bash
   # SSH 連接後查看日誌
   cd /volume1/web/fraud-prevention-platform
   tail -f logs/app.log
   ```

### 重新啟動服務

1. **透過 Web Station**
   - 虛擬主機列表 → 停用 → 啟用

2. **透過 SSH**
   ```bash
   # 重新啟動 Node.js 應用程式
   cd /volume1/web/fraud-prevention-platform
   npm restart
   ```

### 更新應用程式

1. **備份資料**
   ```bash
   cp data/data.json backup/data-$(date +%Y%m%d).json
   ```

2. **更新代碼**
   - 上傳新的程式文件
   - 重新啟動虛擬主機

## 🆘 故障排除

### 常見問題

1. **虛擬主機無法啟動**
   - 檢查 Node.js 套件是否正確安裝
   - 確認 package.json 中的依賴是否完整
   - 檢查連接埠是否被占用

2. **無法訪問網站**
   - 確認防火牆設定
   - 檢查虛擬主機狀態
   - 驗證連接埠設定

3. **效能問題**
   - DS223j 記憶體有限，監控資源使用
   - 考慮關閉不必要的套件
   - 調整 Node.js 記憶體限制

### 日誌位置

- **Web Station 日誌**：`/var/log/nginx/`
- **Node.js 應用程式日誌**：`/volume1/web/fraud-prevention-platform/logs/`
- **系統日誌**：控制台 → 日誌中心

## 📈 效能優化

### DS223j 特定優化

1. **記憶體管理**
   ```javascript
   // 在 server.js 中添加記憶體限制
   if (process.env.NODE_ENV === 'production') {
       process.env.NODE_OPTIONS = '--max-old-space-size=256';
   }
   ```

2. **快取設定**
   - 啟用靜態文件快取
   - 設定適當的快取標頭

3. **資源監控**
   - 定期檢查 CPU 和記憶體使用情況
   - 必要時重新啟動服務

## 🌐 外網訪問設定

### 路由器設定

1. **連接埠轉發**
   - 外部連接埠：8080 (建議使用非標準連接埠)
   - 內部 IP：NAS_IP
   - 內部連接埠：3001

2. **DDNS 設定**
   - 控制台 → 外部存取 → DDNS
   - 設定動態 DNS 服務

### 安全建議

- 🔐 使用強密碼和雙重驗證
- 🔥 定期更新 DSM 和套件
- 📊 監控存取日誌
- 🚫 限制管理員帳戶存取

---

**注意事項：**
- DS223j 是入門級 NAS，請監控系統資源使用情況
- 建議在低峰時段進行維護和更新
- 定期備份重要資料和設定
