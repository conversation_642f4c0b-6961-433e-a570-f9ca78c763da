# 🌐 Web Station 部署指南

## 📋 系統需求

- Synology NAS (如 DS223j)
- DSM 7.0 或更新版本
- Node.js 套件
- 至少 500MB 可用空間

## 🚀 部署步驟

### 步驟 1: 安裝必要套件

1. **登入 DSM**
2. **開啟套件中心**
3. **安裝以下套件**：
   - **Node.js v18** (或更新版本)
   - **Web Station** (可選，用於管理)

### 步驟 2: 上傳專案文件

1. **開啟 File Station**
2. **創建目錄**：`/volume1/web/fraud-prevention-platform`
3. **上傳所有專案文件**到此目錄

### 步驟 3: 執行部署腳本

```bash
# SSH 連接到 NAS
ssh admin@你的NAS_IP

# 切換到專案目錄
cd /volume1/web/fraud-prevention-platform

# 執行部署腳本
chmod +x webstation-setup.sh
./webstation-setup.sh
```

### 步驟 4: 啟動應用程式

```bash
# 啟動應用程式
./start-app.sh
```

### 步驟 5: 訪問網站

- **內網**：`http://NAS_IP:3001`
- **外網**：需設定路由器連接埠轉發

## 🔧 管理命令

### 基本操作
```bash
# 啟動服務
./start-app.sh

# 停止服務
./stop-app.sh

# 重新啟動
./restart-app.sh

# 檢查狀態
./status-app.sh

# 查看日誌
tail -f logs/app.log
```

### 更新應用程式
```bash
# 停止服務
./stop-app.sh

# 上傳新文件
# (使用 File Station 或 SCP)

# 重新安裝依賴（如果 package.json 有變更）
npm install --production

# 重新啟動
./start-app.sh
```

## 🔄 開機自動啟動

### 使用任務排程器

1. **控制台 → 任務排程器**
2. **建立 → 觸發的任務 → 使用者定義的指令碼**
3. **設定**：
   ```
   任務名稱：防詐騙平台自動啟動
   使用者：admin
   事件：開機
   指令碼：/volume1/web/fraud-prevention-platform/start-app.sh
   ```

## 🌐 外網訪問設定

### 路由器連接埠轉發
1. **登入路由器管理介面**
2. **設定連接埠轉發**：
   ```
   外部連接埠：8080
   內部 IP：你的NAS_IP
   內部連接埠：3001
   協定：TCP
   ```

### 防火牆設定
**DSM 控制台** → **安全性** → **防火牆**
- 新增規則允許 TCP 連接埠 3001

## 🛡️ 安全設定

### 更改預設密鑰
編輯 `.env` 文件：
```bash
JWT_SECRET=你的超級安全密鑰
```

### 限制訪問 (可選)
在防火牆中設定 IP 白名單

## 📊 監控和維護

### 檢查應用程式狀態
```bash
# 檢查狀態
./status-app.sh

# 查看進程
ps aux | grep "node server.js"

# 檢查連接埠
netstat -tulpn | grep :3001
```

### 備份資料
```bash
# 備份使用者資料
cp data/data.json backup/data-$(date +%Y%m%d).json

# 備份整個專案
tar -czf backup/fraud-prevention-$(date +%Y%m%d).tar.gz .
```

### 日誌管理
```bash
# 查看即時日誌
tail -f logs/app.log

# 清理舊日誌
find logs/ -name "*.log" -mtime +7 -delete
```

## 🆘 故障排除

### 應用程式無法啟動
```bash
# 檢查 Node.js
node --version

# 檢查依賴
npm list

# 檢查連接埠占用
netstat -tulpn | grep :3001

# 查看詳細錯誤
cat logs/app.log
```

### 無法訪問網站
1. 確認應用程式正在運行：`./status-app.sh`
2. 檢查防火牆設定
3. 確認連接埠 3001 未被其他程式占用

### 記憶體不足 (DS223j)
```bash
# 檢查記憶體使用
free -h

# 重新啟動應用程式
./restart-app.sh

# 重新啟動 NAS (如果需要)
sudo reboot
```

## 🎯 與 Docker 部署的比較

### Web Station 優勢
- ✅ **資源消耗更少** - 直接運行，無容器開銷
- ✅ **設定更簡單** - 無需 Docker 知識
- ✅ **維護更容易** - 直接管理檔案和進程
- ✅ **啟動更快** - 無需容器啟動時間

### 注意事項
- ⚠️ **環境依賴** - 需要正確安裝 Node.js
- ⚠️ **權限管理** - 需要注意檔案權限
- ⚠️ **進程管理** - 需要手動管理應用程式進程

---

## 🎉 部署完成

您的防詐騙教學平台現在已經在 Web Station 環境中運行！

### 功能確認
- ✅ 防詐騙測驗 (25 題)
- ✅ 資安測驗 (35 題)
- ✅ 金融常識測驗 (34 題)
- ✅ 詐騙模擬體驗
- ✅ 使用者註冊登入
- ✅ 學習進度追蹤

### 訪問方式
- **內網**：`http://你的NAS_IP:3001`
- **外網**：`http://你的公網IP:8080` (需設定路由器)

現在您可以享受更簡單、更直接的 Web Station 部署方式！
