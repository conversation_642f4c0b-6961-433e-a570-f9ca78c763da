#!/bin/bash
cd "$(dirname "$0")"

# 載入環境變數
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 檢查是否已經在運行
if [ -f app.pid ] && kill -0 $(cat app.pid) 2>/dev/null; then
    echo "應用程式已在運行中 (PID: $(cat app.pid))"
    exit 1
fi

# 啟動應用程式
echo "啟動防詐騙教學平台..."
nohup node server.js > logs/app.log 2>&1 &
echo $! > app.pid

sleep 2
if kill -0 $(cat app.pid) 2>/dev/null; then
    echo "✅ 應用程式啟動成功 (PID: $(cat app.pid))"
    echo "🌐 訪問網址: http://$(ip route get 1 | awk '{print $7}' | head -1):${PORT:-9999}"
else
    echo "❌ 應用程式啟動失敗"
    rm -f app.pid
    exit 1
fi
