<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超值購物網 - 全台最低價保證 (詐騙模擬)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
        }
        .fake-header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 10px 0;
        }
        .fake-navbar {
            background: #2d3436;
        }
        .product-card {
            border: 2px solid #e74c3c;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        .fake-price {
            font-size: 24px;
            color: #e74c3c;
            font-weight: bold;
        }
        .original-price {
            text-decoration: line-through;
            color: #6c757d;
            font-size: 16px;
        }
        .urgent-notice {
            background: #ff6b6b;
            color: white;
            padding: 10px;
            text-align: center;
            animation: pulse 1s infinite;
        }
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(220, 53, 69, 0.95);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .warning-content {
            max-width: 700px;
            padding: 40px;
            background: white;
            color: #333;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-height: 90vh;
            overflow-y: auto;
        }
        .pulse-animation {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <!-- 詐騙警告覆蓋層 -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-4x pulse-animation"></i>
            </div>
            <h2 class="text-danger mb-3">🚨 這是詐騙購物網站！🚨</h2>
            <div class="alert alert-danger">
                <h5>您差點被騙了！</h5>
                <p class="mb-0">幸好這只是教育模擬，但在真實情況下，您可能已經被騙錢了！</p>
            </div>
            
            <div class="text-start mt-4">
                <h6 class="text-danger">這個假購物網站的詐騙特徵：</h6>
                <ul class="text-start">
                    <li>商品價格異常便宜（iPhone 只要 3,000 元）</li>
                    <li>網站設計粗糙，有明顯的語法錯誤</li>
                    <li>缺乏完整的公司資訊和聯絡方式</li>
                    <li>催促立即下單，製造緊迫感</li>
                    <li>只接受特定付款方式（通常是轉帳）</li>
                    <li>沒有客服電話或地址資訊</li>
                </ul>
                
                <h6 class="text-success">如何避免購物詐騙：</h6>
                <ul class="text-start">
                    <li>✅ 選擇知名且有信譽的購物平台</li>
                    <li>✅ 檢查網站的公司資訊和聯絡方式</li>
                    <li>✅ 閱讀其他買家的真實評價</li>
                    <li>✅ 使用安全的付款方式（信用卡、第三方支付）</li>
                    <li>✅ 價格過低要特別小心</li>
                    <li>✅ 確認退換貨政策</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="goBackToSimulation()">
                    <i class="fas fa-graduation-cap me-2"></i>我學會了
                </button>
                <button class="btn btn-primary btn-lg" onclick="goHome()">
                    <i class="fas fa-home me-2"></i>回到首頁
                </button>
            </div>
        </div>
    </div>

    <!-- 假購物網站內容 -->
    <div class="fake-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        超值購物網
                    </h3>
                    <small>全台最低價保證！不滿意退費！</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <i class="fas fa-phone me-2"></i>客服專線：0800-000-000
                </div>
            </div>
        </div>
    </div>

    <nav class="fake-navbar navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <div class="navbar-nav">
                <a class="nav-link" href="#">首頁</a>
                <a class="nav-link" href="#">3C產品</a>
                <a class="nav-link" href="#">服飾配件</a>
                <a class="nav-link" href="#">居家用品</a>
                <a class="nav-link" href="#">特價專區</a>
            </div>
        </div>
    </nav>

    <div class="urgent-notice">
        <strong>🔥 限時搶購！今日下單享超低價！錯過不再有！🔥</strong>
    </div>

    <div class="container my-4">
        <div class="row">
            <div class="col-md-8">
                <div class="product-card bg-white p-4">
                    <div class="discount-badge">-90%</div>
                    <div class="row">
                        <div class="col-md-6">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2Y4ZjlmYSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSIjNmM3NTdkIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+aVBob25lIDE1IFBybzwvdGV4dD4KICA8L3N2Zz4K" 
                                 class="img-fluid rounded" alt="iPhone 15 Pro">
                        </div>
                        <div class="col-md-6">
                            <h4>iPhone 15 Pro 256GB</h4>
                            <div class="mb-3">
                                <span class="fake-price">NT$ 3,000</span>
                                <span class="original-price ms-2">NT$ 30,000</span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <span class="ms-2">5.0 (999+ 評價)</span>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-clock me-2"></i>
                                <strong>限時優惠！</strong>剩餘時間：
                                <span id="countdown" class="text-danger fw-bold">02:34:56</span>
                            </div>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>全新未拆封</li>
                                <li><i class="fas fa-check text-success me-2"></i>原廠保固</li>
                                <li><i class="fas fa-check text-success me-2"></i>免運費</li>
                                <li><i class="fas fa-check text-success me-2"></i>7天鑑賞期</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h5>商品詳情</h5>
                    <p>全新 iPhone 15 Pro，原價 30,000 元，現在只要 3,000 元！數量有限，售完為止！</p>
                    <p>※ 此為清倉特價，不接受退換貨</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>立即購買
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="fakeOrderForm">
                            <div class="mb-3">
                                <label class="form-label">收件人姓名</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">手機號碼</label>
                                <input type="tel" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">收件地址</label>
                                <textarea class="form-control" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">付款方式</label>
                                <select class="form-select" required>
                                    <option value="">請選擇</option>
                                    <option value="transfer">銀行轉帳（享額外折扣）</option>
                                    <option value="cod" disabled>貨到付款（暫停服務）</option>
                                    <option value="credit" disabled>信用卡（系統維護中）</option>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-danger btn-lg">
                                    <i class="fas fa-bolt me-2"></i>搶購下單
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                已有 <span class="text-danger fw-bold">1,234</span> 人購買
                            </small>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-body">
                        <h6>🔥 熱銷商品</h6>
                        <div class="d-flex align-items-center mb-2">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjZjhmOWZhIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM2Yzc1N2QiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5BaXJQb2RzPC90ZXh0Pgo8L3N2Zz4K" 
                                 class="me-2" alt="AirPods">
                            <div>
                                <small>AirPods Pro</small><br>
                                <span class="text-danger fw-bold">NT$ 500</span>
                                <small class="text-muted"><s>NT$ 8,000</s></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 倒數計時器
        function updateCountdown() {
            const countdownElement = document.getElementById('countdown');
            let time = countdownElement.textContent.split(':');
            let hours = parseInt(time[0]);
            let minutes = parseInt(time[1]);
            let seconds = parseInt(time[2]);
            
            seconds--;
            if (seconds < 0) {
                seconds = 59;
                minutes--;
                if (minutes < 0) {
                    minutes = 59;
                    hours--;
                    if (hours < 0) {
                        hours = 2;
                        minutes = 34;
                        seconds = 56;
                    }
                }
            }
            
            countdownElement.textContent = 
                String(hours).padStart(2, '0') + ':' +
                String(minutes).padStart(2, '0') + ':' +
                String(seconds).padStart(2, '0');
        }
        
        setInterval(updateCountdown, 1000);
        
        // 表單提交處理
        document.getElementById('fakeOrderForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>處理中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                document.getElementById('warningOverlay').style.display = 'flex';
            }, 2000);
        });
        
        function goBackToSimulation() {
            window.location.href = '/scam-simulation';
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // 輸入提示
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (!this.hasAttribute('data-warned')) {
                    this.setAttribute('data-warned', 'true');
                    this.style.borderColor = '#dc3545';
                    
                    const tooltip = document.createElement('div');
                    tooltip.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    tooltip.style.top = '20px';
                    tooltip.style.right = '20px';
                    tooltip.style.zIndex = '1050';
                    tooltip.style.maxWidth = '300px';
                    tooltip.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>真實情況下絕對不要在可疑網站購物！
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(tooltip);
                    
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.remove();
                        }
                    }, 5000);
                }
            });
        });
    </script>
</body>
</html>
