# 🌐 Apache + PHP 部署方案

## 📋 概述
將 Node.js 應用程式轉換為 PHP 版本，使用 Synology 內建的 Apache Web Server。

## ✅ 優勢
- 使用 Synology 內建 Web Server
- 自動處理外網訪問
- 更穩定，資源消耗更少
- 不需要複雜的連接埠設定

## 🚀 部署步驟

### 步驟 1: 安裝套件
1. **套件中心** → 搜尋並安裝：
   - **Web Station**
   - **Apache HTTP Server 2.4**
   - **PHP 8.0** (或更新版本)

### 步驟 2: 啟用 Web Station
1. 開啟 **Web Station**
2. **一般設定**：
   - HTTP 後端伺服器：Apache HTTP Server 2.4
   - PHP：PHP 8.0
   - 啟用個人網站：✓

### 步驟 3: 創建 PHP 版本
我會為您創建簡化的 PHP 版本，保留核心功能。

## 📁 文件結構
```
/volume1/web/fraud-prevention/
├── index.php (首頁)
├── quiz.php (測驗頁面)
├── scam-simulation.php (詐騙模擬)
├── data/
│   └── users.json (使用者資料)
├── assets/
│   ├── css/
│   └── js/
└── includes/
    ├── config.php
    ├── functions.php
    └── questions.php
```

## 🌐 訪問方式
- **內網**：http://NAS_IP/fraud-prevention/
- **外網**：http://公網IP/fraud-prevention/ (自動支援)
