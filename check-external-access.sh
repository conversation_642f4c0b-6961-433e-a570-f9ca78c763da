#!/bin/bash

# 外網訪問診斷腳本

echo "🔍 外網訪問診斷工具"
echo "===================="

# 檢查應用程式狀態
echo "1. 檢查應用程式狀態..."
if ps aux | grep -q "node server.js"; then
    echo "✅ 應用程式正在運行"
    PID=$(ps aux | grep "node server.js" | grep -v grep | awk '{print $2}')
    echo "   PID: $PID"
else
    echo "❌ 應用程式未運行"
    echo "   請先啟動應用程式: ./start-app.sh"
    exit 1
fi

# 檢查連接埠監聽
echo ""
echo "2. 檢查連接埠監聽..."
LISTEN_INFO=$(netstat -tulpn 2>/dev/null | grep :3000 || ss -tulpn 2>/dev/null | grep :3000)
if [ -n "$LISTEN_INFO" ]; then
    echo "✅ 連接埠 3000 正在監聽"
    echo "   $LISTEN_INFO"
    
    if echo "$LISTEN_INFO" | grep -q "0.0.0.0:3000"; then
        echo "✅ 綁定到所有網路介面 (0.0.0.0)"
    elif echo "$LISTEN_INFO" | grep -q "127.0.0.1:3000"; then
        echo "❌ 只綁定到 localhost (127.0.0.1)"
        echo "   這是外網無法訪問的主要原因！"
        echo "   解決方案：修改 server.js，使用 app.listen(PORT, '0.0.0.0', ...)"
    fi
else
    echo "❌ 連接埠 3000 未監聽"
fi

# 檢查本機 IP
echo ""
echo "3. 檢查網路介面..."
LOCAL_IPS=$(ip addr show 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | cut -d'/' -f1)
if [ -n "$LOCAL_IPS" ]; then
    echo "✅ 本機 IP 地址："
    for ip in $LOCAL_IPS; do
        echo "   $ip"
        echo "   內網測試: http://$ip:3000"
    done
else
    echo "❌ 無法獲取本機 IP"
fi

# 檢查防火牆 (如果可以的話)
echo ""
echo "4. 檢查防火牆..."
if command -v iptables >/dev/null 2>&1; then
    if iptables -L INPUT 2>/dev/null | grep -q "3000"; then
        echo "✅ 發現防火牆規則涉及連接埠 3000"
    else
        echo "⚠️  未發現防火牆規則，請檢查 DSM 防火牆設定"
    fi
else
    echo "ℹ️  無法檢查 iptables，請手動檢查 DSM 防火牆設定"
fi

# 檢查公網 IP
echo ""
echo "5. 檢查公網 IP..."
PUBLIC_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || curl -s --connect-timeout 5 ipinfo.io/ip 2>/dev/null)
if [ -n "$PUBLIC_IP" ]; then
    echo "✅ 公網 IP: $PUBLIC_IP"
    echo "   外網測試 (需要路由器連接埠轉發): http://$PUBLIC_IP:8080"
else
    echo "❌ 無法獲取公網 IP (可能網路問題)"
fi

# 檢查路由器連接
echo ""
echo "6. 檢查路由器連接..."
GATEWAY=$(ip route show default 2>/dev/null | awk '{print $3}' | head -1)
if [ -n "$GATEWAY" ]; then
    echo "✅ 預設閘道: $GATEWAY"
    echo "   路由器管理介面: http://$GATEWAY"
    
    if ping -c 1 -W 2 "$GATEWAY" >/dev/null 2>&1; then
        echo "✅ 可以連接到路由器"
    else
        echo "❌ 無法連接到路由器"
    fi
else
    echo "❌ 無法獲取預設閘道"
fi

# 測試內網連接
echo ""
echo "7. 測試內網連接..."
for ip in $LOCAL_IPS; do
    if curl -s --connect-timeout 3 "http://$ip:3000" >/dev/null 2>&1; then
        echo "✅ 內網連接正常: http://$ip:3000"
    else
        echo "❌ 內網連接失敗: http://$ip:3000"
    fi
done

# 提供解決建議
echo ""
echo "🔧 解決建議："
echo "============"

echo ""
echo "如果外網無法訪問，請按順序檢查："
echo ""
echo "1. 應用程式綁定問題："
echo "   - 確保 server.js 中使用 app.listen(PORT, '0.0.0.0', ...)"
echo "   - 重新啟動應用程式: ./restart-app.sh"
echo ""
echo "2. Synology 防火牆："
echo "   - 控制台 → 安全性 → 防火牆"
echo "   - 新增規則允許 TCP 連接埠 3000"
echo ""
echo "3. 路由器連接埠轉發："
echo "   - 登入路由器管理介面: http://$GATEWAY"
echo "   - 設定連接埠轉發: 外部 8080 → $LOCAL_IP:3000"
echo ""
echo "4. 測試外網連接："
echo "   - 使用手機數據 (關閉 WiFi)"
echo "   - 訪問: http://$PUBLIC_IP:8080"
echo ""
echo "5. 線上連接埠檢查："
echo "   - 訪問: https://www.yougetsignal.com/tools/open-ports/"
echo "   - 輸入公網 IP: $PUBLIC_IP 和連接埠: 8080"

echo ""
echo "📋 需要的資訊："
echo "=============="
echo "內網 IP: $(echo $LOCAL_IPS | awk '{print $1}')"
echo "公網 IP: $PUBLIC_IP"
echo "路由器 IP: $GATEWAY"
echo "應用程式連接埠: 3000"
echo "建議外部連接埠: 8080"
