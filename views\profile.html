<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>個人資料 - 防詐騙教學平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                防詐教學平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首頁</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            學習專區
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/quiz/fraud">防詐騙測驗</a></li>
                            <li><a class="dropdown-item" href="/quiz/cybersecurity">資安測驗</a></li>
                            <li><a class="dropdown-item" href="/quiz/finance">金融常識測驗</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/progress">學習進度</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item" id="loginNav">
                        <a class="nav-link" href="/login">登入</a>
                    </li>
                    <li class="nav-item" id="registerNav">
                        <a class="nav-link" href="/register">註冊</a>
                    </li>
                    <li class="nav-item d-none" id="userNav">
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <span id="username"></span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item active" href="/profile">個人資料</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()">登出</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div id="alert-container"></div>
        
        <div id="login-required" class="text-center py-5 d-none">
            <i class="fas fa-lock fa-3x text-muted mb-3"></i>
            <h3>需要登入</h3>
            <p class="text-muted">請先登入以查看您的個人資料</p>
            <a href="/login" class="btn btn-primary">立即登入</a>
        </div>

        <div id="profile-content" class="d-none">
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card auth-card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-user-edit me-2"></i>
                                個人資料
                            </h4>
                        </div>
                        <div class="card-body p-4">
                            <!-- 個人資訊顯示 -->
                            <div id="profile-display">
                                <div class="row mb-4">
                                    <div class="col-12 text-center">
                                        <div class="profile-avatar mb-3">
                                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                                        </div>
                                        <h5 id="display-username">載入中...</h5>
                                        <p class="text-muted" id="display-email">載入中...</p>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong>使用者名稱：</strong>
                                    </div>
                                    <div class="col-sm-8" id="profile-username">
                                        載入中...
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong>電子郵件：</strong>
                                    </div>
                                    <div class="col-sm-8" id="profile-email">
                                        載入中...
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong>註冊時間：</strong>
                                    </div>
                                    <div class="col-sm-8" id="profile-created">
                                        載入中...
                                    </div>
                                </div>
                                
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <strong>帳號狀態：</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge bg-success">正常</span>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <button class="btn btn-primary me-2" onclick="showEditForm()">
                                        <i class="fas fa-edit me-2"></i>編輯資料
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="showChangePasswordForm()">
                                        <i class="fas fa-key me-2"></i>變更密碼
                                    </button>
                                </div>
                            </div>

                            <!-- 編輯個人資料表單 -->
                            <div id="profile-edit" class="d-none">
                                <form id="editProfileForm">
                                    <div class="mb-3">
                                        <label for="edit-username" class="form-label">使用者名稱</label>
                                        <input type="text" class="form-control" id="edit-username" name="username" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="edit-email" class="form-label">電子郵件</label>
                                        <input type="email" class="form-control" id="edit-email" name="email" required>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-success me-2">
                                            <i class="fas fa-save me-2"></i>儲存變更
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                                            <i class="fas fa-times me-2"></i>取消
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- 變更密碼表單 -->
                            <div id="password-change" class="d-none">
                                <form id="changePasswordForm">
                                    <div class="mb-3">
                                        <label for="current-password" class="form-label">目前密碼</label>
                                        <input type="password" class="form-control" id="current-password" name="currentPassword" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="new-password" class="form-label">新密碼</label>
                                        <input type="password" class="form-control" id="new-password" name="newPassword" required>
                                        <div class="form-text">密碼至少需要8個字符，包含大小寫字母和數字</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirm-password" class="form-label">確認新密碼</label>
                                        <input type="password" class="form-control" id="confirm-password" name="confirmPassword" required>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-warning me-2">
                                            <i class="fas fa-key me-2"></i>變更密碼
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="cancelPasswordChange()">
                                            <i class="fas fa-times me-2"></i>取消
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
            <p class="mt-3">載入個人資料中...</p>
        </div>
    </div>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>防詐騙 × 資安 × 金融常識 智能線上教學平台</h5>
                    <p class="text-muted">提升全民數位素養，共同打造安全的網路環境。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">© 2024 All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/profile.js"></script>
</body>
</html>
