# 🚀 Web Station 簡化部署指南 (無虛擬主機)

## 📋 適用情況
- Web Station 沒有虛擬主機選項
- 想要簡單快速部署
- 使用預設網站功能

## ⚡ 快速部署步驟

### 步驟 1: 檢查 Web Station 設定

1. **開啟 Web Station**
2. **檢查一般設定**
   - 確認 HTTP 連接埠 (通常是 80)
   - 確認 HTTPS 連接埠 (通常是 443)
   - 記下預設網站根目錄路徑

3. **啟用 PHP 和其他功能**
   - 如果有選項，啟用 Node.js 支援

### 步驟 2: 手動啟動 Node.js 應用程式

由於沒有虛擬主機，我們需要手動管理 Node.js 應用程式：

1. **上傳文件到 web 目錄**
   ```
   /volume1/web/fraud-prevention-platform/
   ```

2. **SSH 連接並安裝依賴**
   ```bash
   ssh admin@你的NAS_IP
   cd /volume1/web/fraud-prevention-platform
   npm install
   ```

3. **創建啟動腳本**
   ```bash
   # 創建啟動腳本
   cat > start-app.sh << 'EOF'
   #!/bin/bash
   cd /volume1/web/fraud-prevention-platform
   export NODE_ENV=production
   export JWT_SECRET=your-secret-key-change-this
   export PORT=3001
   nohup node server.js > logs/app.log 2>&1 &
   echo $! > app.pid
   echo "應用程式已啟動，PID: $(cat app.pid)"
   EOF
   
   chmod +x start-app.sh
   ```

4. **創建停止腳本**
   ```bash
   cat > stop-app.sh << 'EOF'
   #!/bin/bash
   cd /volume1/web/fraud-prevention-platform
   if [ -f app.pid ]; then
       PID=$(cat app.pid)
       kill $PID
       rm app.pid
       echo "應用程式已停止"
   else
       echo "找不到 PID 文件"
   fi
   EOF
   
   chmod +x stop-app.sh
   ```

5. **啟動應用程式**
   ```bash
   ./start-app.sh
   ```

### 步驟 3: 設定自動啟動 (可選)

1. **創建系統服務腳本**
   ```bash
   sudo cat > /etc/systemd/system/fraud-prevention.service << 'EOF'
   [Unit]
   Description=Fraud Prevention Platform
   After=network.target
   
   [Service]
   Type=forking
   User=admin
   WorkingDirectory=/volume1/web/fraud-prevention-platform
   ExecStart=/volume1/web/fraud-prevention-platform/start-app.sh
   ExecStop=/volume1/web/fraud-prevention-platform/stop-app.sh
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   EOF
   ```

2. **啟用服務**
   ```bash
   sudo systemctl enable fraud-prevention
   sudo systemctl start fraud-prevention
   ```

### 步驟 4: 訪問網站

應用程式將在連接埠 3001 運行：
```
http://你的NAS_IP:3001
```

## 🔧 管理命令

### 啟動應用程式
```bash
cd /volume1/web/fraud-prevention-platform
./start-app.sh
```

### 停止應用程式
```bash
cd /volume1/web/fraud-prevention-platform
./stop-app.sh
```

### 重新啟動應用程式
```bash
./stop-app.sh && ./start-app.sh
```

### 查看日誌
```bash
tail -f logs/app.log
```

### 檢查狀態
```bash
# 檢查進程是否運行
ps aux | grep "node server.js"

# 檢查連接埠
netstat -tulpn | grep :3001
```

## 🛡️ 防火牆設定

1. **DSM 防火牆**
   - 控制台 → 安全性 → 防火牆
   - 新增規則允許連接埠 3001

2. **路由器設定 (外網訪問)**
   - 連接埠轉發：8080 → NAS_IP:3001

## 🔄 開機自動啟動

### 方法 1: 使用 DSM 任務排程器

1. **控制台 → 任務排程器**
2. **建立 → 觸發的任務 → 使用者定義的指令碼**
3. **設定**：
   ```
   任務名稱：啟動防詐騙平台
   使用者：admin
   事件：開機
   指令碼：/volume1/web/fraud-prevention-platform/start-app.sh
   ```

### 方法 2: 修改啟動腳本

在 `/usr/local/etc/rc.d/` 創建啟動腳本：

```bash
sudo cat > /usr/local/etc/rc.d/fraud-prevention.sh << 'EOF'
#!/bin/sh
case $1 in
    start)
        su - admin -c "/volume1/web/fraud-prevention-platform/start-app.sh"
        ;;
    stop)
        su - admin -c "/volume1/web/fraud-prevention-platform/stop-app.sh"
        ;;
esac
EOF

sudo chmod +x /usr/local/etc/rc.d/fraud-prevention.sh
```

## 🆘 故障排除

### 應用程式無法啟動
1. 檢查 Node.js 是否安裝：`node --version`
2. 檢查依賴是否安裝：`npm list`
3. 檢查連接埠是否被占用：`netstat -tulpn | grep :3001`

### 無法訪問網站
1. 檢查應用程式是否運行：`ps aux | grep node`
2. 檢查防火牆設定
3. 檢查日誌：`tail -f logs/app.log`

### 記憶體不足
DS223j 記憶體有限，如果遇到問題：
1. 重新啟動 NAS
2. 關閉不必要的套件
3. 調整 Node.js 記憶體限制

## 📊 監控和維護

### 定期維護腳本
```bash
cat > maintenance.sh << 'EOF'
#!/bin/bash
echo "=== 防詐騙平台維護 ==="
echo "1. 檢查應用程式狀態"
if ps aux | grep -q "node server.js"; then
    echo "✅ 應用程式正在運行"
else
    echo "❌ 應用程式未運行，嘗試重新啟動"
    ./start-app.sh
fi

echo "2. 檢查磁碟空間"
df -h /volume1

echo "3. 檢查記憶體使用"
free -h

echo "4. 清理舊日誌 (保留最近7天)"
find logs/ -name "*.log" -mtime +7 -delete

echo "=== 維護完成 ==="
EOF

chmod +x maintenance.sh
```

### 設定定期維護
在任務排程器中設定每週執行維護腳本。

---

這個方案不依賴 Web Station 的虛擬主機功能，直接使用 Node.js 運行應用程式，更適合您的情況。
