#!/bin/bash

# 防詐騙教學平台 Web Station 部署腳本
# 適用於 Synology NAS Web Station

echo "🚀 開始設定防詐騙教學平台 (Web Station)..."

# 檢查是否在正確的目錄
if [ ! -f "server.js" ]; then
    echo "❌ 請在專案根目錄執行此腳本"
    exit 1
fi

# 檢查 Node.js 是否安裝
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安裝，請先在套件中心安裝 Node.js"
    exit 1
fi

# 檢查 npm 是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未找到，請確認 Node.js 安裝正確"
    exit 1
fi

echo "📦 安裝 Node.js 依賴..."
npm install --production

if [ $? -ne 0 ]; then
    echo "❌ 依賴安裝失敗"
    exit 1
fi

# 創建必要的目錄
echo "📁 創建必要目錄..."
mkdir -p data
mkdir -p logs

# 設定檔案權限
echo "🔐 設定檔案權限..."
chmod 755 server.js
chmod -R 755 public
chmod -R 755 views
chmod 755 webstation-setup.sh

# 創建環境變數文件（如果不存在）
if [ ! -f ".env" ]; then
    echo "⚙️  創建環境變數文件..."
    cat > .env << EOF
NODE_ENV=production
PORT=3001
JWT_SECRET=$(openssl rand -base64 32)
LOG_LEVEL=info
EOF
    echo "✅ 已創建 .env 文件，JWT_SECRET 已自動生成"
else
    echo "ℹ️  .env 文件已存在，跳過創建"
fi

# 測試應用程式啟動
echo "🧪 測試應用程式..."
timeout 10s node server.js &
TEST_PID=$!
sleep 3

if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ 應用程式測試成功"
    kill $TEST_PID
else
    echo "❌ 應用程式測試失敗，請檢查錯誤訊息"
    exit 1
fi

echo ""
echo "🎉 設定完成！"
echo ""
echo "📋 接下來的步驟："
echo "1. 開啟 Web Station 套件"
echo "2. 建立虛擬主機："
echo "   - 主機名稱: fraud-prevention"
echo "   - 連接埠: 3001"
echo "   - 文件根目錄: $(pwd)"
echo "   - 後端伺服器: Node.js"
echo "   - 啟動檔案: server.js"
echo ""
echo "3. 環境變數設定："
echo "   - NODE_ENV=production"
echo "   - JWT_SECRET=$(grep JWT_SECRET .env | cut -d'=' -f2)"
echo "   - PORT=3001"
echo ""
echo "4. 啟用虛擬主機後訪問："
echo "   http://$(hostname -I | awk '{print $1}'):3001"
echo ""
echo "📚 詳細說明請參考 webstation-deploy-guide.md"
