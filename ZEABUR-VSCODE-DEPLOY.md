# 🚀 VSCode + Zeabur 擴充套件部署指南

## 📋 準備工作

### 已完成的配置
我已經為您創建了以下配置文件：
- ✅ `zeabur.json` - Zeabur 專案配置
- ✅ `.zeaburignore` - 忽略不需要上傳的文件
- ✅ 調整了 `server.js` 的連接埠設定

## 🚀 部署步驟

### 步驟 1: 安裝 Zeabur 擴充套件

1. **開啟 VSCode**
2. **按 Ctrl+Shift+X** 開啟擴充套件市場
3. **搜尋 "Zeabur"**
4. **點擊安裝** Zeabur 官方擴充套件

### 步驟 2: 登入 Zeabur

1. **按 Ctrl+Shift+P** 開啟命令面板
2. **輸入 "Zeabur: Login"**
3. **選擇登入方式**：
   - GitHub 帳號 (推薦)
   - Google 帳號
   - 或註冊新帳號

### 步驟 3: 部署專案

1. **在 VSCode 中開啟您的專案資料夾**
2. **按 Ctrl+Shift+P** 開啟命令面板
3. **輸入 "Zeabur: Deploy"**
4. **選擇部署選項**：
   - 創建新專案：選擇 "Create new project"
   - 專案名稱：`fraud-prevention-platform`

### 步驟 4: 配置環境變數

部署過程中，Zeabur 會要求設定環境變數：

```
NODE_ENV=production
JWT_SECRET=請更改為您的安全密鑰
PORT=3001
```

**重要**：請將 `JWT_SECRET` 更改為您自己的安全密鑰！

### 步驟 5: 等待部署完成

1. **VSCode 會顯示部署進度**
2. **部署完成後會提供網址**
3. **格式**：`https://your-app-name.zeabur.app`

## 🌐 訪問您的網站

部署完成後，您會獲得：
- **Zeabur 網址**：`https://fraud-prevention-platform-xxx.zeabur.app`
- **自動 HTTPS**：免費 SSL 憑證
- **全球 CDN**：快速訪問

## 🔧 管理和更新

### 在 VSCode 中管理

1. **查看部署狀態**：
   - 命令面板 → "Zeabur: Show Projects"

2. **查看日誌**：
   - 命令面板 → "Zeabur: Show Logs"

3. **重新部署**：
   - 修改代碼後
   - 命令面板 → "Zeabur: Deploy"

### 在 Zeabur Dashboard 管理

1. **前往** https://dash.zeabur.com
2. **功能**：
   - 查看部署日誌
   - 監控資源使用
   - 設定自定義域名
   - 管理環境變數

## 🔄 自動部署 (可選)

如果您想要自動部署：

1. **連接 Git 倉庫**：
   - 在 Zeabur Dashboard 中
   - 選擇 "Connect Git Repository"

2. **推送代碼自動部署**：
   - 每次 git push 都會自動重新部署

## 💰 費用說明

### 免費額度 (足夠個人使用)
- **運算時間**：每月 100 小時
- **流量**：每月 100GB
- **儲存空間**：1GB
- **自定義域名**：支援

### 如果超出免費額度
- **Starter 方案**：$5/月
- **Pro 方案**：$20/月

## 🛡️ 安全設定

### 環境變數安全
- ✅ JWT_SECRET 已設為環境變數
- ✅ 敏感資料不會暴露在代碼中
- ✅ Zeabur 自動加密環境變數

### HTTPS 安全
- ✅ 自動 HTTPS 憑證
- ✅ 強制 HTTPS 重定向
- ✅ 現代 TLS 加密

## 🆘 故障排除

### 部署失敗
1. **檢查 package.json**：
   - 確保有 `"start": "node server.js"`
   - 確保所有依賴都在 `dependencies` 中

2. **檢查日誌**：
   - VSCode 命令面板 → "Zeabur: Show Logs"

### 應用程式無法訪問
1. **檢查連接埠**：
   - 確保使用 `process.env.PORT`
   - 我已經為您設定為 3001

2. **檢查環境變數**：
   - 在 Zeabur Dashboard 確認環境變數設定

### 功能異常
1. **資料持久化**：
   - 目前使用記憶體資料庫
   - 重新部署會清空資料
   - 如需持久化，可升級使用 Zeabur 的資料庫服務

## 📋 部署檢查清單

- [ ] VSCode 已安裝 Zeabur 擴充套件
- [ ] 已登入 Zeabur 帳號
- [ ] 專案已成功部署
- [ ] 環境變數已設定 (特別是 JWT_SECRET)
- [ ] 網址可以正常訪問
- [ ] 註冊/登入功能正常
- [ ] 測驗功能正常
- [ ] 詐騙模擬功能正常

## 🎉 部署完成後

您的防詐騙教學平台現在已經：
- ✅ **全球可訪問** - 任何人都能使用
- ✅ **自動 HTTPS** - 安全連接
- ✅ **高可用性** - Zeabur 保證 99.9% 正常運行時間
- ✅ **自動擴展** - 根據使用量自動調整資源

## 🌟 分享您的平台

部署完成後，您可以：
1. **分享網址**給朋友、同事、學生
2. **設定自定義域名**（如 fraud-prevention.your-domain.com）
3. **監控使用情況**在 Zeabur Dashboard

---

## 🚀 立即開始

現在就可以開始部署：
1. 安裝 Zeabur 擴充套件
2. 登入 Zeabur 帳號
3. 執行 "Zeabur: Deploy" 命令
4. 等待部署完成

整個過程大約 5-10 分鐘！
