document.addEventListener('DOMContentLoaded', function() {
    setupScamCards();
});

function setupScamCards() {
    const scamCards = document.querySelectorAll('.scam-card');
    scamCards.forEach(card => {
        card.addEventListener('click', function() {
            const scamType = this.getAttribute('data-scam');
            startScamSimulation(scamType);
        });
    });
}

function startScamSimulation(scamType) {
    // 顯示警告對話框
    showWarningModal(scamType);
}

function showWarningModal(scamType) {
    const scamInfo = getScamInfo(scamType);
    
    const modalHtml = `
        <div class="modal fade" id="warningModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            準備進入詐騙模擬
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-shield-alt me-2"></i>重要提醒</h6>
                            <p class="mb-0">您即將體驗 <strong>${scamInfo.name}</strong> 的模擬。<br>
                            這是純教育用途，但請記住在真實情況下<strong>絕對不要</strong>輸入真實資料！</p>
                        </div>
                        
                        <h6>這種詐騙的特徵：</h6>
                        <ul>
                            ${scamInfo.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                        
                        <h6>防範要點：</h6>
                        <ul>
                            ${scamInfo.prevention.map(tip => `<li>${tip}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>取消
                        </button>
                        <button type="button" class="btn btn-danger" onclick="proceedToSimulation('${scamType}')">
                            <i class="fas fa-play me-2"></i>開始模擬體驗
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除舊的 modal
    const existingModal = document.getElementById('warningModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 添加新的 modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // 顯示 modal
    const modal = new bootstrap.Modal(document.getElementById('warningModal'));
    modal.show();
}

function proceedToSimulation(scamType) {
    // 關閉警告 modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('warningModal'));
    modal.hide();
    
    // 根據詐騙類型跳轉到對應的模擬頁面
    setTimeout(() => {
        window.location.href = `/scam-simulation/${scamType}`;
    }, 500);
}

function getScamInfo(scamType) {
    const scamData = {
        'phishing-bank': {
            name: '釣魚銀行網站',
            features: [
                '網址與真實銀行相似但有細微差異',
                '要求輸入完整的帳號密碼',
                '通常透過釣魚郵件或簡訊連結進入',
                '頁面設計模仿真實銀行網站',
                '可能有拼字錯誤或版面異常'
            ],
            prevention: [
                '直接輸入銀行官方網址，不要點擊連結',
                '檢查網址是否為 HTTPS 且正確',
                '銀行不會透過郵件要求輸入密碼',
                '使用銀行官方 APP 進行操作',
                '發現可疑立即聯繫銀行客服'
            ]
        },
        'fake-shopping': {
            name: '假購物網站',
            features: [
                '商品價格異常便宜',
                '網站設計粗糙或有明顯錯誤',
                '只接受特定付款方式',
                '缺乏完整的公司資訊',
                '客服聯絡方式不明確'
            ],
            prevention: [
                '選擇知名且有信譽的購物平台',
                '檢查網站的公司資訊和聯絡方式',
                '閱讀其他買家的評價',
                '使用安全的付款方式',
                '價格過低要特別小心'
            ]
        },
        'fake-lottery': {
            name: '假中獎通知',
            features: [
                '聲稱中了從未參加的抽獎',
                '要求先付手續費或稅金',
                '催促立即行動',
                '要求提供個人敏感資訊',
                '聯絡方式不是官方管道'
            ],
            prevention: [
                '記住：沒參加的抽獎不可能中獎',
                '真正的中獎不需要先付費',
                '向相關機構查證',
                '不要提供個人資料',
                '保持冷靜，不要被催促'
            ]
        },
        'fake-investment': {
            name: '假投資平台',
            features: [
                '承諾不合理的高報酬',
                '聲稱零風險或保證獲利',
                '要求快速決定',
                '缺乏合法的金融牌照',
                '推薦人可能是陌生網友'
            ],
            prevention: [
                '記住：高報酬必然伴隨高風險',
                '檢查是否有合法的金融牌照',
                '不要相信網路陌生人的投資建議',
                '諮詢專業的理財顧問',
                '分散投資，不要孤注一擲'
            ]
        },
        'fake-support': {
            name: '假客服詐騙',
            features: [
                '主動聯繫聲稱帳戶異常',
                '要求提供帳號密碼驗證身份',
                '催促立即處理',
                '聯絡方式不是官方管道',
                '可能冒充知名公司'
            ],
            prevention: [
                '真正的客服不會主動要求密碼',
                '透過官方管道聯繫客服',
                '不要透露個人敏感資訊',
                '保持冷靜，不要被催促',
                '有疑慮時掛斷電話重新撥打'
            ]
        },
        'romance-scam': {
            name: '感情詐騙',
            features: [
                '快速建立親密關係',
                '聲稱遇到緊急狀況需要金錢',
                '拒絕見面或視訊通話',
                '個人資料可能是假的',
                '經常要求匯款或轉帳'
            ],
            prevention: [
                '對網路交友保持警覺',
                '不要輕易匯款給網友',
                '要求視訊通話確認身份',
                '與親友討論交友狀況',
                '感情和金錢要分開處理'
            ]
        }
    };
    
    return scamData[scamType] || {
        name: '未知詐騙類型',
        features: ['請小心任何可疑的網站或訊息'],
        prevention: ['保持警覺，保護個人資料']
    };
}
