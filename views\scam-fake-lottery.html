<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 恭喜中獎！台灣大樂透官方通知 (詐騙模擬)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            min-height: 100vh;
            font-family: 'Microsoft JhengHei', sans-serif;
        }
        .lottery-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            overflow: hidden;
            border: 5px solid #ffd700;
        }
        .header-banner {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .header-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: shine 3s linear infinite;
        }
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        .prize-amount {
            font-size: 48px;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: pulse 2s infinite;
        }
        .blink {
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        .urgent-notice {
            background: #dc3545;
            color: white;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: center;
            animation: pulse 1.5s infinite;
        }
        .form-container {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 20px;
        }
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(220, 53, 69, 0.95);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .warning-content {
            max-width: 700px;
            padding: 40px;
            background: white;
            color: #333;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-height: 90vh;
            overflow-y: auto;
        }
        .pulse-animation {
            animation: pulse 1s infinite;
        }
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ffd700;
            animation: confetti-fall 3s linear infinite;
        }
        @keyframes confetti-fall {
            0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
            100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
        }
    </style>
</head>
<body>
    <!-- 詐騙警告覆蓋層 -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-4x pulse-animation"></i>
            </div>
            <h2 class="text-danger mb-3">🚨 這是中獎詐騙！🚨</h2>
            <div class="alert alert-danger">
                <h5>您差點被騙了！</h5>
                <p class="mb-0">幸好這只是教育模擬，但在真實情況下，您可能已經被騙取個人資料和金錢了！</p>
            </div>
            
            <div class="text-start mt-4">
                <h6 class="text-danger">這個假中獎通知的詐騙特徵：</h6>
                <ul class="text-start">
                    <li>聲稱中了從未參加的抽獎活動</li>
                    <li>要求先付手續費、稅金或保證金</li>
                    <li>催促立即行動，製造緊迫感</li>
                    <li>要求提供個人敏感資訊（身分證、銀行帳號）</li>
                    <li>聯絡方式不是官方管道</li>
                    <li>中獎金額異常巨大</li>
                </ul>
                
                <h6 class="text-success">如何避免中獎詐騙：</h6>
                <ul class="text-start">
                    <li>✅ 記住：沒參加的抽獎不可能中獎</li>
                    <li>✅ 真正的中獎不需要先付任何費用</li>
                    <li>✅ 向相關機構（如台彩）查證</li>
                    <li>✅ 不要提供個人敏感資料</li>
                    <li>✅ 保持冷靜，不要被催促</li>
                    <li>✅ 與家人朋友討論再決定</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="goBackToSimulation()">
                    <i class="fas fa-graduation-cap me-2"></i>我學會了
                </button>
                <button class="btn btn-primary btn-lg" onclick="goHome()">
                    <i class="fas fa-home me-2"></i>回到首頁
                </button>
            </div>
        </div>
    </div>

    <!-- 假中獎通知內容 -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="lottery-container">
                    <div class="header-banner">
                        <h1 class="mb-3">
                            <i class="fas fa-trophy me-2"></i>
                            🎉 台灣大樂透官方通知 🎉
                        </h1>
                        <h2 class="blink">恭喜您中獎了！</h2>
                    </div>
                    
                    <div class="p-4 text-center">
                        <div class="mb-4">
                            <h3 class="text-success mb-3">
                                <i class="fas fa-medal me-2"></i>
                                頭獎得主確認通知
                            </h3>
                            <div class="prize-amount">NT$ 50,000,000</div>
                            <p class="lead">五千萬元整</p>
                        </div>
                        
                        <div class="urgent-notice">
                            <h5><i class="fas fa-clock me-2"></i>緊急通知</h5>
                            <p class="mb-0">您的中獎資格將在 <strong class="blink">24小時內</strong> 失效！<br>
                            請立即填寫以下資料完成領獎程序！</p>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>中獎資訊</h6>
                            <div class="row text-start">
                                <div class="col-md-6">
                                    <p><strong>開獎期別：</strong>第112000123期</p>
                                    <p><strong>開獎日期：</strong>2024年01月15日</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>中獎號碼：</strong>07, 12, 23, 31, 42, 49</p>
                                    <p><strong>特別號：</strong>18</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-container">
                        <h5 class="text-center mb-4 text-danger">
                            <i class="fas fa-edit me-2"></i>
                            請填寫領獎資料（必填）
                        </h5>
                        
                        <form id="fakeLotteryForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">真實姓名 *</label>
                                    <input type="text" class="form-control" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">身分證字號 *</label>
                                    <input type="text" class="form-control" placeholder="A123456789" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">手機號碼 *</label>
                                    <input type="tel" class="form-control" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">電子郵件 *</label>
                                    <input type="email" class="form-control" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">銀行帳號（用於獎金匯入）*</label>
                                <input type="text" class="form-control" placeholder="請輸入完整銀行帳號" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">銀行名稱 *</label>
                                <select class="form-select" required>
                                    <option value="">請選擇銀行</option>
                                    <option value="台灣銀行">台灣銀行</option>
                                    <option value="第一銀行">第一銀行</option>
                                    <option value="華南銀行">華南銀行</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>重要提醒</h6>
                                <p class="mb-2">根據稅法規定，需先繳納獎金稅額 <strong class="text-danger">NT$ 500,000</strong></p>
                                <p class="mb-0">請於確認後24小時內匯款至指定帳戶，完成稅務程序後即可領取獎金。</p>
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="agree" required>
                                <label class="form-check-label" for="agree">
                                    我同意提供以上資料並了解相關稅務規定
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-danger btn-lg blink">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    立即確認領獎
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                客服專線：0800-000-000 (24小時服務)
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-dark">
                        <i class="fas fa-shield-alt me-1"></i>
                        台灣彩券官方認證 | 合法經營許可證號：ABC-123456
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 彩帶效果 -->
    <div class="confetti" style="left: 10%; animation-delay: 0s; background: #ff6b6b;"></div>
    <div class="confetti" style="left: 20%; animation-delay: 0.5s; background: #4ecdc4;"></div>
    <div class="confetti" style="left: 30%; animation-delay: 1s; background: #45b7d1;"></div>
    <div class="confetti" style="left: 40%; animation-delay: 1.5s; background: #96ceb4;"></div>
    <div class="confetti" style="left: 50%; animation-delay: 2s; background: #ffeaa7;"></div>
    <div class="confetti" style="left: 60%; animation-delay: 2.5s; background: #dda0dd;"></div>
    <div class="confetti" style="left: 70%; animation-delay: 0.3s; background: #98d8c8;"></div>
    <div class="confetti" style="left: 80%; animation-delay: 0.8s; background: #f7dc6f;"></div>
    <div class="confetti" style="left: 90%; animation-delay: 1.3s; background: #bb8fce;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表單提交處理
        document.getElementById('fakeLotteryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>處理中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                document.getElementById('warningOverlay').style.display = 'flex';
            }, 2000);
        });
        
        function goBackToSimulation() {
            window.location.href = '/scam-simulation';
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // 輸入提示
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (!this.hasAttribute('data-warned')) {
                    this.setAttribute('data-warned', 'true');
                    this.style.borderColor = '#dc3545';
                    
                    const tooltip = document.createElement('div');
                    tooltip.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    tooltip.style.top = '20px';
                    tooltip.style.right = '20px';
                    tooltip.style.zIndex = '1050';
                    tooltip.style.maxWidth = '300px';
                    tooltip.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>真實情況下絕對不要提供個人資料！
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(tooltip);
                    
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.remove();
                        }
                    }, 5000);
                }
            });
        });
    </script>
</body>
</html>
