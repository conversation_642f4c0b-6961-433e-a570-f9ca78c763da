# 🐳 Docker 一鍵部署指南

## 🎯 為什麼選擇 Docker？
- ✅ **自動解決網路問題** - Docker 自動處理連接埠映射
- ✅ **外網訪問簡單** - 只需設定路由器一次
- ✅ **環境隔離** - 不影響 NAS 其他服務
- ✅ **容易管理** - 一鍵啟動/停止/更新

## 🚀 超簡單部署 (5分鐘)

### 步驟 1: 安裝 Docker (1分鐘)
1. **DSM 套件中心** → 搜尋 **Docker** → 安裝

### 步驟 2: 上傳文件 (2分鐘)
1. **File Station** → 創建資料夾 `/docker/fraud-prevention`
2. 上傳所有專案文件到此資料夾

### 步驟 3: 一鍵部署 (2分鐘)
```bash
# SSH 連接
ssh admin@你的NAS_IP

# 切換到專案目錄
cd /volume1/docker/fraud-prevention

# 一鍵啟動
docker-compose up -d
```

### 步驟 4: 完成！
- **內網訪問**：`http://NAS_IP:3000`
- **外網訪問**：只需在路由器設定一次連接埠轉發

## 🔧 Docker 管理命令

### 基本操作
```bash
# 啟動服務
docker-compose up -d

# 停止服務
docker-compose down

# 重新啟動
docker-compose restart

# 查看狀態
docker-compose ps

# 查看日誌
docker-compose logs -f
```

### 更新應用程式
```bash
# 停止服務
docker-compose down

# 重新構建
docker-compose build --no-cache

# 啟動服務
docker-compose up -d
```

## 🌐 外網訪問設定

### 路由器連接埠轉發 (一次設定)
1. **登入路由器管理介面**
2. **連接埠轉發設定**：
   ```
   外部連接埠：8080
   內部 IP：你的NAS_IP
   內部連接埠：3000
   協定：TCP
   ```
3. **儲存設定**

### 訪問網址
- **內網**：`http://NAS_IP:3000`
- **外網**：`http://公網IP:8080`

## 🛡️ 安全設定

### 1. 更改預設密鑰
編輯 `docker-compose.yml`：
```yaml
environment:
  - JWT_SECRET=你的超級安全密鑰
```

### 2. 防火牆設定
**DSM 控制台** → **安全性** → **防火牆**
- 允許連接埠：3000 (TCP)

### 3. 限制訪問 (可選)
在 `docker-compose.yml` 中限制綁定 IP：
```yaml
ports:
  - "*************:3000:3000"  # 只允許特定 IP
```

## 📊 監控和維護

### 檢查容器狀態
```bash
# 查看運行中的容器
docker ps

# 查看容器詳細資訊
docker inspect fraud-prevention-platform

# 查看資源使用
docker stats fraud-prevention-platform
```

### 備份資料
```bash
# 備份使用者資料
docker cp fraud-prevention-platform:/app/data ./backup/

# 或直接備份本地資料夾
cp -r data backup/data-$(date +%Y%m%d)
```

### 日誌管理
```bash
# 查看即時日誌
docker-compose logs -f

# 查看最近 100 行日誌
docker-compose logs --tail=100

# 清理日誌 (如果太大)
docker-compose down
docker system prune -f
docker-compose up -d
```

## 🆘 故障排除

### 容器無法啟動
```bash
# 檢查錯誤訊息
docker-compose logs

# 檢查連接埠是否被占用
netstat -tulpn | grep :3000

# 強制重新構建
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 外網無法訪問
1. **檢查容器狀態**：`docker-compose ps`
2. **檢查連接埠映射**：`docker port fraud-prevention-platform`
3. **測試內網訪問**：`curl http://localhost:3000`
4. **檢查路由器設定**：確認連接埠轉發正確

### 效能問題 (DS223j)
```bash
# 限制容器記憶體使用
# 在 docker-compose.yml 中添加：
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '0.5'
```

## 🎉 Docker 部署的優勢

### 相比直接部署
- ✅ **網路問題自動解決** - Docker 處理所有網路設定
- ✅ **環境一致性** - 在任何 Synology NAS 上都能運行
- ✅ **容易備份和遷移** - 整個應用程式容器化
- ✅ **版本管理** - 可以輕鬆回滾到之前版本

### 相比其他方案
- ✅ **比 PHP 轉換簡單** - 保持原有功能
- ✅ **比套件開發快速** - 無需複雜打包過程
- ✅ **比 Web Station 穩定** - 不依賴特定套件版本

## 📋 部署檢查清單

- [ ] Docker 套件已安裝
- [ ] 專案文件已上傳到 `/volume1/docker/fraud-prevention`
- [ ] 執行 `docker-compose up -d`
- [ ] 容器狀態正常：`docker-compose ps`
- [ ] 內網訪問正常：`http://NAS_IP:3000`
- [ ] 路由器連接埠轉發已設定：8080 → NAS_IP:3000
- [ ] 外網訪問測試：`http://公網IP:8080`

---

## 🚀 立即開始

現在就可以開始 Docker 部署了！這是最簡單、最可靠的方案，能夠一次性解決所有網路訪問問題。

如果您準備好了，請告訴我，我可以協助您完成整個部署過程！
