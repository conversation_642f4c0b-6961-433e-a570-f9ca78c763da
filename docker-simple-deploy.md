# 🐳 Docker 簡化部署方案

## 📋 概述
使用 Docker 容器部署，自動處理網路設定和外網訪問。

## ✅ 優勢
- 自動處理網路設定
- 隔離環境，更安全
- 容易管理和更新
- 支援外網訪問

## 🚀 快速部署

### 步驟 1: 安裝 Docker
1. **套件中心** → 搜尋並安裝 **Docker**

### 步驟 2: 創建 Docker 配置
我會為您創建簡化的 Docker 配置文件。

### 步驟 3: 一鍵部署
```bash
# SSH 連接
ssh admin@你的NAS_IP
cd /你的專案目錄

# 構建並啟動
docker-compose up -d
```

### 步驟 4: 訪問
- **內網**：http://NAS_IP:3000
- **外網**：自動支援 (Docker 處理網路)

## 🔧 管理命令
```bash
# 啟動
docker-compose up -d

# 停止
docker-compose down

# 查看日誌
docker-compose logs -f

# 重新啟動
docker-compose restart
```
