# 🌐 外網訪問設定指南

## 🔍 問題診斷

### 當前狀況確認
- ✅ 內網可訪問：`http://NAS_IP:3000`
- ❌ 外網無法訪問

## 🛠️ 解決步驟

### 步驟 1: 檢查應用程式綁定

確認應用程式是否只綁定到 localhost：

```bash
# SSH 連接到 NAS
ssh admin@你的NAS_IP

# 檢查應用程式監聽的地址
netstat -tulpn | grep :3000
# 或
ss -tulpn | grep :3000
```

**期望結果**：
```
tcp    0.0.0.0:3000    # 綁定到所有介面 (正確)
tcp    127.0.0.1:3000  # 只綁定到 localhost (問題所在)
```

如果顯示 `127.0.0.1:3000`，需要修改應用程式設定。

### 步驟 2: 修改伺服器綁定設定

編輯 `server.js` 文件，確保綁定到所有網路介面：

```javascript
// 在 server.js 的最後部分，找到類似這樣的代碼：
app.listen(PORT, () => {
    console.log(`伺服器運行在 http://localhost:${PORT}`);
});

// 修改為：
app.listen(PORT, '0.0.0.0', () => {
    console.log(`伺服器運行在 http://0.0.0.0:${PORT}`);
    console.log(`內網訪問：http://你的NAS_IP:${PORT}`);
});
```

### 步驟 3: Synology 防火牆設定

1. **開啟 DSM 控制台**
2. **安全性 → 防火牆**
3. **編輯規則** 或 **新增規則**：
   ```
   規則名稱：防詐騙平台外網訪問
   連接埠：TCP 3000
   來源 IP：全部 (或指定允許的 IP 範圍)
   動作：允許
   ```
4. **套用設定**

### 步驟 4: 路由器連接埠轉發設定

這是最關鍵的步驟！

#### 4.1 找到路由器管理介面
```
通常是以下網址之一：
http://***********
http://***********
http://********
```

#### 4.2 設定連接埠轉發 (Port Forwarding)
1. **登入路由器管理介面**
2. **找到「連接埠轉發」或「虛擬伺服器」設定**
   - 可能在：進階設定、NAT、網路設定等選單
3. **新增規則**：
   ```
   服務名稱：防詐騙平台
   外部連接埠：8080 (建議不用 3000，避免安全風險)
   內部 IP：你的NAS_IP (例如 *************)
   內部連接埠：3000
   協定：TCP
   狀態：啟用
   ```

#### 4.3 常見路由器品牌設定位置
- **華碩 (ASUS)**：進階設定 → WAN → 虛擬伺服器/連接埠轉發
- **TP-Link**：進階 → NAT 轉發 → 虛擬伺服器
- **D-Link**：進階 → 連接埠轉發
- **Netgear**：動態 DNS → 連接埠轉發/連接埠觸發
- **小米路由器**：進階設定 → 連接埠轉發

### 步驟 5: 取得公網 IP

#### 5.1 檢查公網 IP
```bash
# 方法 1：在 NAS 上執行
curl ifconfig.me

# 方法 2：訪問網站
# 在瀏覽器開啟：https://whatismyipaddress.com/
```

#### 5.2 檢查是否為固定 IP
- **固定 IP**：可直接使用
- **動態 IP**：需要設定 DDNS (動態 DNS)

### 步驟 6: 設定 DDNS (動態 IP 用戶)

#### 6.1 在 Synology 設定 DDNS
1. **控制台 → 外部存取 → DDNS**
2. **新增**：
   ```
   服務供應商：Synology (推薦) 或其他
   主機名稱：你選擇的名稱.synology.me
   使用者名稱/密碼：你的 Synology 帳號
   ```
3. **測試連線**

#### 6.2 其他 DDNS 服務
- **No-IP**：免費，需定期確認
- **DuckDNS**：免費，簡單易用
- **Cloudflare**：功能強大，有免費方案

### 步驟 7: 測試外網連接

#### 7.1 使用手機數據測試
關閉 WiFi，使用手機數據訪問：
```
http://你的公網IP:8080
# 或 (如果設定了 DDNS)
http://你的域名.synology.me:8080
```

#### 7.2 使用線上工具測試
- **連接埠檢查**：https://www.yougetsignal.com/tools/open-ports/
- **輸入你的公網 IP 和連接埠 8080**

## 🔧 進階設定

### 反向代理設定 (可選)

如果想使用標準 HTTP 連接埠 (80)：

1. **控制台 → 應用程式入口網站 → 反向代理**
2. **建立**：
   ```
   描述：防詐騙平台
   來源：
   - 協定：HTTP
   - 主機名稱：* (或你的域名)
   - 連接埠：80
   
   目的地：
   - 協定：HTTP
   - 主機名稱：localhost
   - 連接埠：3000
   ```

### SSL 憑證設定 (推薦)

1. **控制台 → 安全性 → 憑證**
2. **新增 → Let's Encrypt**
3. **套用到反向代理**

## 🆘 故障排除

### 常見問題檢查清單

#### 1. 應用程式層面
```bash
# 檢查應用程式是否運行
ps aux | grep "node server.js"

# 檢查監聽地址
netstat -tulpn | grep :3000

# 檢查日誌
tail -f logs/app.log
```

#### 2. 防火牆層面
```bash
# 檢查 iptables 規則 (進階)
sudo iptables -L -n

# 檢查 DSM 防火牆狀態
# 控制台 → 安全性 → 防火牆
```

#### 3. 網路層面
```bash
# 從 NAS 測試外網連接
curl -I http://google.com

# 檢查路由
ip route show
```

#### 4. 路由器層面
- 確認連接埠轉發規則已儲存
- 檢查路由器是否需要重新啟動
- 確認 UPnP 設定 (如果使用)

### 測試步驟

#### 逐步測試連接
```bash
# 1. 本機測試
curl http://localhost:3000

# 2. 內網測試
curl http://NAS_IP:3000

# 3. 路由器內網測試
curl http://路由器IP:8080

# 4. 外網測試 (使用手機數據)
curl http://公網IP:8080
```

## 📋 檢查清單

### 設定完成確認
- [ ] 應用程式綁定到 0.0.0.0:3000
- [ ] Synology 防火牆允許連接埠 3000
- [ ] 路由器連接埠轉發：8080 → NAS_IP:3000
- [ ] 公網 IP 已確認
- [ ] DDNS 已設定 (動態 IP 用戶)
- [ ] 使用手機數據測試成功

### 安全建議
- [ ] 使用非標準外部連接埠 (如 8080 而非 3000)
- [ ] 設定 SSL 憑證
- [ ] 限制來源 IP (如果可能)
- [ ] 定期更新系統和應用程式
- [ ] 監控訪問日誌

---

## 🎯 快速解決方案

如果上述步驟太複雜，最簡單的解決方案：

1. **確認應用程式綁定**：修改 server.js 綁定到 0.0.0.0
2. **路由器連接埠轉發**：8080 → NAS_IP:3000
3. **手機數據測試**：http://公網IP:8080

大多數情況下，問題出在路由器的連接埠轉發設定上！
