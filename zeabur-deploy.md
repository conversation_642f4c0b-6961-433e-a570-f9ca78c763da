# 🌐 Zeabur 雲端部署指南

## 📋 概述
Zeabur 是一個現代化的雲端部署平台，支援自動部署、HTTPS、自定義域名等功能。

## ✅ 優勢
- ✅ **零配置部署** - 自動識別 Node.js 專案
- ✅ **自動 HTTPS** - 免費 SSL 憑證
- ✅ **全球 CDN** - 快速訪問
- ✅ **自動擴展** - 根據流量自動調整
- ✅ **免費額度** - 適合個人專案

## 🚀 部署步驟

### 步驟 1: 準備 Git 倉庫

1. **創建 GitHub 倉庫**
   - 前往 https://github.com
   - 點擊 "New repository"
   - 倉庫名稱：`fraud-prevention-platform`
   - 設為 Public（免費用戶）

2. **推送代碼到 GitHub**
   ```bash
   # 在您的專案目錄中
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/你的用戶名/fraud-prevention-platform.git
   git push -u origin main
   ```

### 步驟 2: 註冊 Zeabur

1. **前往 Zeabur**
   - 訪問：https://zeabur.com
   - 點擊 "Sign Up" 或 "Get Started"

2. **使用 GitHub 登入**
   - 選擇 "Continue with GitHub"
   - 授權 Zeabur 訪問您的 GitHub

### 步驟 3: 創建專案

1. **創建新專案**
   - 在 Zeabur Dashboard 點擊 "Create Project"
   - 專案名稱：`fraud-prevention-platform`

2. **添加服務**
   - 點擊 "Add Service"
   - 選擇 "Git Repository"
   - 選擇您的 `fraud-prevention-platform` 倉庫

### 步驟 4: 配置環境變數

在 Zeabur 專案中設定環境變數：

```
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key-change-this
PORT=3000
```

### 步驟 5: 部署

1. **自動部署**
   - Zeabur 會自動檢測到 Node.js 專案
   - 自動執行 `npm install` 和 `npm start`

2. **獲取網址**
   - 部署完成後，Zeabur 會提供一個網址
   - 格式：`https://your-app.zeabur.app`

## 🌐 訪問方式

- **Zeabur 網址**：`https://your-app.zeabur.app`
- **自定義域名**：可以綁定您自己的域名

## 🔧 管理和監控

### Zeabur Dashboard 功能
- **部署日誌** - 查看部署過程
- **應用程式日誌** - 查看運行日誌
- **資源監控** - CPU、記憶體使用情況
- **環境變數管理** - 動態修改配置
- **域名管理** - 綁定自定義域名

### 自動部署
- 每次推送到 GitHub main 分支
- Zeabur 會自動重新部署
- 支援 Git 分支部署

## 💰 費用說明

### 免費額度
- **運算時間**：每月 100 小時
- **流量**：每月 100GB
- **儲存空間**：1GB
- **自定義域名**：支援

### 付費方案
- **Starter**：$5/月 - 適合小型專案
- **Pro**：$20/月 - 適合商業專案

## 🔧 進階配置

### 自定義域名
1. **在 Zeabur Dashboard**
   - 進入專案 → Settings → Domains
   - 添加您的域名

2. **DNS 設定**
   - 在您的域名提供商設定 CNAME
   - 指向 Zeabur 提供的地址

### 資料庫整合
如果需要持久化資料庫：

1. **添加 PostgreSQL 服務**
   - 在專案中點擊 "Add Service"
   - 選擇 "PostgreSQL"

2. **修改應用程式**
   - 更新 server.js 使用 PostgreSQL
   - 設定資料庫連接環境變數

## 🆘 故障排除

### 部署失敗
1. **檢查 package.json**
   - 確保有正確的 `start` 腳本
   - 確保所有依賴都在 `dependencies` 中

2. **檢查日誌**
   - 在 Zeabur Dashboard 查看部署日誌
   - 查看應用程式運行日誌

### 應用程式無法訪問
1. **檢查連接埠**
   - 確保應用程式監聽 `process.env.PORT`
   - Zeabur 會自動設定 PORT 環境變數

2. **檢查環境變數**
   - 確保所有必要的環境變數都已設定

## 📋 部署檢查清單

- [ ] GitHub 倉庫已創建並推送代碼
- [ ] Zeabur 帳號已註冊
- [ ] 專案已在 Zeabur 創建
- [ ] 環境變數已設定
- [ ] 應用程式成功部署
- [ ] 網址可以正常訪問
- [ ] 所有功能測試通過

## 🎯 與 NAS 部署的比較

### Zeabur 優勢
- ✅ **零維護** - 無需管理伺服器
- ✅ **全球訪問** - 自動 CDN 加速
- ✅ **自動 HTTPS** - 免費 SSL 憑證
- ✅ **自動擴展** - 根據流量調整
- ✅ **專業監控** - 完整的監控面板

### NAS 部署優勢
- ✅ **完全控制** - 自己的硬體
- ✅ **無流量限制** - 不受雲端服務限制
- ✅ **隱私保護** - 資料在自己手中
- ✅ **長期成本** - 無月費

## 🚀 立即開始

現在就可以開始 Zeabur 部署：

1. **推送代碼到 GitHub**
2. **註冊 Zeabur 帳號**
3. **創建專案並部署**
4. **設定環境變數**
5. **測試訪問**

整個過程大約 10-15 分鐘就能完成！
