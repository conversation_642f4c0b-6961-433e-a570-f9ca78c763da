<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 真愛交友 - 尋找您的另一半 (詐騙模擬)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            min-height: 100vh;
            font-family: 'Microsoft JhengHei', sans-serif;
        }
        .romance-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header-banner {
            background: linear-gradient(45deg, #ff6b9d, #c44569);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .profile-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 20px 0;
        }
        .chat-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .message {
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 20px;
            max-width: 80%;
        }
        .message.received {
            background: #e3f2fd;
            margin-right: auto;
        }
        .message.sent {
            background: #ff6b9d;
            color: white;
            margin-left: auto;
        }
        .urgent-request {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            animation: pulse 2s infinite;
        }
        .warning-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(220, 53, 69, 0.95);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        .warning-content {
            max-width: 700px;
            padding: 40px;
            background: white;
            color: #333;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-height: 90vh;
            overflow-y: auto;
        }
        .pulse-animation {
            animation: pulse 1s infinite;
        }
        .heart-animation {
            animation: heartbeat 1.5s ease-in-out infinite;
        }
        @keyframes heartbeat {
            0% { transform: scale(1); }
            14% { transform: scale(1.1); }
            28% { transform: scale(1); }
            42% { transform: scale(1.1); }
            70% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <!-- 詐騙警告覆蓋層 -->
    <div class="warning-overlay" id="warningOverlay">
        <div class="warning-content">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-4x pulse-animation"></i>
            </div>
            <h2 class="text-danger mb-3">🚨 這是感情詐騙！🚨</h2>
            <div class="alert alert-danger">
                <h5>您差點被騙了！</h5>
                <p class="mb-0">幸好這只是教育模擬，但在真實情況下，您可能已經被騙取金錢和感情了！</p>
            </div>
            
            <div class="text-start mt-4">
                <h6 class="text-danger">這個感情詐騙的特徵：</h6>
                <ul class="text-start">
                    <li>快速建立親密關係，表達強烈愛意</li>
                    <li>聲稱遇到緊急狀況需要金錢幫助</li>
                    <li>拒絕見面或視訊通話</li>
                    <li>個人資料可能是假的或盜用他人照片</li>
                    <li>經常要求匯款或轉帳</li>
                    <li>故事情節過於戲劇化或完美</li>
                    <li>避談具體的個人細節</li>
                </ul>
                
                <h6 class="text-success">如何避免感情詐騙：</h6>
                <ul class="text-start">
                    <li>✅ 對網路交友保持理性和警覺</li>
                    <li>✅ 不要輕易匯款給網友</li>
                    <li>✅ 要求視訊通話確認身份</li>
                    <li>✅ 與親友討論交友狀況</li>
                    <li>✅ 感情和金錢要分開處理</li>
                    <li>✅ 用反向圖片搜尋檢查照片真實性</li>
                    <li>✅ 注意對方是否避談見面</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="goBackToSimulation()">
                    <i class="fas fa-graduation-cap me-2"></i>我學會了
                </button>
                <button class="btn btn-primary btn-lg" onclick="goHome()">
                    <i class="fas fa-home me-2"></i>回到首頁
                </button>
            </div>
        </div>
    </div>

    <!-- 感情詐騙模擬內容 -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="romance-container">
                    <div class="header-banner">
                        <h1 class="mb-2">
                            <i class="fas fa-heart heart-animation me-2"></i>
                            💕 真愛交友平台 💕
                        </h1>
                        <p class="mb-0">找到您的真命天子/天女 | 已成功配對 50,000+ 對情侶</p>
                    </div>
                    
                    <div class="row p-4">
                        <div class="col-md-4">
                            <div class="profile-card">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNjY5OWZmO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZjY2OWQ7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiLz4KICA8Y2lyY2xlIGN4PSIxNTAiIGN5PSIxMjAiIHI9IjQwIiBmaWxsPSJ3aGl0ZSIvPgogIDxwYXRoIGQ9Ik0xMTAgMjAwIEMxMTAgMTgwIDEzMCAxNjAgMTUwIDE2MCBTMTkwIDE4MCAyMDAgMjAwIEwxMDAgMjAwIFoiIGZpbGw9IndoaXRlIi8+CiAgPHRleHQgeD0iMTUwIiB5PSIyNTAiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7nvo7lpb3nmoTlpJbnsbPkurrlj6s8L3RleHQ+Cjwvc3ZnPgo=" 
                                     class="card-img-top" alt="David 的照片">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        David Chen <span class="badge bg-success">在線</span>
                                    </h5>
                                    <p class="text-muted">32歲 | 醫生 | 台北</p>
                                    <div class="mb-3">
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <i class="fas fa-star text-warning"></i>
                                        <span class="ms-2">5.0 (完美配對)</span>
                                    </div>
                                    <p class="small">
                                        <i class="fas fa-heart text-danger me-2"></i>
                                        "尋找一個真心相愛的人，一起走過人生的每個階段。我是一名外科醫生，工作穩定，希望能找到我的真愛。"
                                    </p>
                                    <div class="text-center">
                                        <span class="badge bg-primary">醫生</span>
                                        <span class="badge bg-info">高收入</span>
                                        <span class="badge bg-success">真誠</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-8">
                            <h5 class="mb-3">
                                <i class="fas fa-comments me-2"></i>
                                與 David 的對話
                            </h5>
                            
                            <div class="chat-container">
                                <div class="message received">
                                    <strong>David:</strong> 你好！看到你的照片覺得你真的很美，想認識你 😊
                                </div>
                                <div class="message sent">
                                    <strong>我:</strong> 謝謝你的讚美，很高興認識你
                                </div>
                                <div class="message received">
                                    <strong>David:</strong> 我是一名外科醫生，平時工作很忙，但看到你就覺得一切都值得了 ❤️
                                </div>
                                <div class="message received">
                                    <strong>David:</strong> 我覺得我們很有緣分，你願意給我一個機會嗎？我想好好照顧你
                                </div>
                                <div class="message sent">
                                    <strong>我:</strong> 你人很好，我們可以慢慢了解
                                </div>
                                <div class="message received">
                                    <strong>David:</strong> 我已經深深愛上你了！你就是我要找的那個人 💕
                                </div>
                                <div class="message received">
                                    <strong>David:</strong> 我想見你，但最近醫院很忙，等我忙完這個手術就來找你
                                </div>
                                <div class="message received">
                                    <strong>David:</strong> 親愛的，我遇到了一些麻煩...
                                </div>
                            </div>
                            
                            <div class="urgent-request">
                                <h5 class="text-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    緊急求助
                                </h5>
                                <div class="message received mb-3">
                                    <strong>David:</strong> 親愛的，我真的很抱歉要跟你說這件事。我在醫院遇到了緊急狀況，有個病人需要立即手術，但醫院的設備出了問題，需要緊急購買一台醫療設備。
                                </div>
                                <div class="message received mb-3">
                                    <strong>David:</strong> 我的錢都在定期存款裡，現在取不出來。能不能請你先幫我墊付 NT$ 50,000？我保證手術完成後立即還你，而且會加倍償還！
                                </div>
                                <div class="message received mb-3">
                                    <strong>David:</strong> 這關係到病人的生命，我真的很急！你是我最信任的人，只有你能幫我了 😢
                                </div>
                                
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-clock me-2"></i>時間緊迫</h6>
                                    <p class="mb-0">手術必須在2小時內進行，請立即匯款到以下帳戶：</p>
                                    <p class="mb-0"><strong>銀行：</strong>台灣銀行</p>
                                    <p class="mb-0"><strong>帳號：</strong>123-456-789012</p>
                                    <p class="mb-0"><strong>戶名：</strong>陳大偉</p>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-heart me-2"></i>
                                        回應 David 的請求
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form id="fakeRomanceForm">
                                        <div class="mb-3">
                                            <label class="form-label">您的回應</label>
                                            <textarea class="form-control" rows="3" placeholder="輸入您想對 David 說的話..." required></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="response" id="help" value="help" required>
                                                <label class="form-check-label" for="help">
                                                    我願意幫助你，立即匯款
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="response" id="hesitate" value="hesitate" required>
                                                <label class="form-check-label" for="hesitate">
                                                    我需要考慮一下
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="response" id="refuse" value="refuse" required>
                                                <label class="form-check-label" for="refuse">
                                                    抱歉，我無法幫助
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-danger btn-lg">
                                                <i class="fas fa-paper-plane me-2"></i>
                                                發送回應
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表單提交處理
        document.getElementById('fakeRomanceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>發送中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                document.getElementById('warningOverlay').style.display = 'flex';
            }, 2000);
        });
        
        function goBackToSimulation() {
            window.location.href = '/scam-simulation';
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // 輸入提示
        const inputs = document.querySelectorAll('textarea, input[type="radio"]');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (!this.hasAttribute('data-warned')) {
                    this.setAttribute('data-warned', 'true');
                    
                    const tooltip = document.createElement('div');
                    tooltip.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                    tooltip.style.top = '20px';
                    tooltip.style.right = '20px';
                    tooltip.style.zIndex = '1050';
                    tooltip.style.maxWidth = '300px';
                    tooltip.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>真實情況下絕對不要匯款給網友！
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(tooltip);
                    
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.remove();
                        }
                    }, 5000);
                }
            });
        });
        
        // 模擬即時訊息
        function addMessage() {
            const chatContainer = document.querySelector('.chat-container');
            const urgentMessages = [
                "親愛的，你還在嗎？病人的情況越來越危急了！",
                "我真的很需要你的幫助，只有你能救這個病人了！",
                "時間不多了，請快點回應我！"
            ];
            
            const randomMessage = urgentMessages[Math.floor(Math.random() * urgentMessages.length)];
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message received';
            messageDiv.innerHTML = `<strong>David:</strong> ${randomMessage} 💔`;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 每30秒添加一條緊急訊息
        setInterval(addMessage, 30000);
    </script>
</body>
</html>
